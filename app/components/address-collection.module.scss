/* 现代简约的地址收集组件样式 */
.address-container {
  max-width: 100%;
  margin: 0;
  background: #ffffff;
  border-radius: 0;
  padding: 0;
  border: none;
  box-shadow: none;
  position: relative;

  @media (prefers-color-scheme: dark) {
    background: #1f2937;
  }
}

.header {
  text-align: left;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;

  @media (prefers-color-scheme: dark) {
    border-bottom-color: #374151;
  }
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #111827;
  letter-spacing: 0;
  line-height: 1.3;

  @media (prefers-color-scheme: dark) {
    color: #f9fafb;
  }

  @media (max-width: 640px) {
    font-size: 1.125rem;
  }

  @media (max-width: 480px) {
    font-size: 1rem;
  }
}

.subtitle {
  font-size: 0.875rem;
  margin: 0;
  color: #6b7280;
  font-weight: 400;
  line-height: 1.5;
  opacity: 1;

  @media (prefers-color-scheme: dark) {
    color: #9ca3af;
  }

  @media (max-width: 640px) {
    font-size: 0.8rem;
  }

  @media (max-width: 480px) {
    font-size: 0.75rem;
  }
}

.form-container {
  margin-bottom: 1rem;

  @media (max-width: 640px) {
    margin-bottom: 0.75rem;
  }
}

/* Stripe AddressElement 自定义样式 */
.form-container :global(.StripeElement) {
  padding: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: #ffffff;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  min-height: 200px;

  @media (prefers-color-scheme: dark) {
    background: #1f2937;
    border-color: #4b5563;
    color: #f9fafb;
  }
}

.form-container :global(.StripeElement--focus) {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  outline: none;

  @media (prefers-color-scheme: dark) {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
}

.form-container :global(.StripeElement--invalid) {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);

  @media (prefers-color-scheme: dark) {
    border-color: #f87171;
    box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.2);
  }
}

.notice {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: center;
  line-height: 1.4;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;

  @media (prefers-color-scheme: dark) {
    background: #374151;
    border-color: #4b5563;
    color: #9ca3af;
  }

  @media (max-width: 640px) {
    font-size: 0.7rem;
    padding: 0.625rem;
  }
}

.payment-tips {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #e5e7eb;
  font-size: 0.7rem;
  color: #059669;
  text-align: left;

  strong {
    font-weight: 600;
  }

  @media (prefers-color-scheme: dark) {
    border-top-color: #4b5563;
    color: #10b981;
  }

  @media (max-width: 640px) {
    font-size: 0.65rem;
  }
}

/* 响应式设计优化 */
@media (max-width: 480px) {
  .address-container {
    margin: 0 0.75rem;
    padding: 1.25rem;
    border-radius: 6px;
  }

  .header {
    margin-bottom: 1.25rem;
  }

  .title {
    font-size: 1.125rem;
  }

  .subtitle {
    font-size: 0.75rem;
  }

  .form-container :global(.StripeElement) {
    padding: 0.625rem;
    font-size: 0.8rem;
  }

  .notice {
    font-size: 0.65rem;
    padding: 0.5rem;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .address-container {
    border-width: 2px;
  }

  .form-container :global(.StripeElement) {
    border-width: 2px;
  }

  .notice {
    border-width: 2px;
  }

  .title {
    font-weight: 700;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .form-container :global(.StripeElement) {
    transition: none;
  }
}

/* 加载状态样式 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;

  @media (prefers-color-scheme: dark) {
    color: #9ca3af;
  }

  .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;

    @media (prefers-color-scheme: dark) {
      border-color: #4b5563;
      border-top-color: #60a5fa;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 错误状态样式 */
.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 0.75rem;
  margin-top: 0.75rem;
  color: #dc2626;
  font-size: 0.75rem;
  text-align: center;

  @media (prefers-color-scheme: dark) {
    background: #450a0a;
    border-color: #7f1d1d;
    color: #fca5a5;
  }
}

/* 成功状态样式 */
.success-message {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 6px;
  padding: 0.75rem;
  margin-top: 0.75rem;
  color: #16a34a;
  font-size: 0.75rem;
  text-align: center;

  @media (prefers-color-scheme: dark) {
    background: #052e16;
    border-color: #166534;
    color: #86efac;
  }
}

/* 自定义表单样式 */
.custom-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  padding: 2rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    pointer-events: none;
  }

  @media (prefers-color-scheme: dark) {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    border-color: rgba(75, 85, 99, 0.8);
  }

  @media (max-width: 640px) {
    padding: 1.5rem;
    border-radius: 12px;
  }
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  position: relative;
  margin-bottom: 0.75rem;
}

.form-label {
  font-size: 0.8rem;
  font-weight: 500;
  color: #6b7280;
  margin: 0;
  letter-spacing: 0;
  transition: color 0.2s ease;

  @media (prefers-color-scheme: dark) {
    color: #9ca3af;
  }
}

.form-select,
.form-input {
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: #ffffff;
  font-size: 0.875rem;
  font-weight: 400;
  transition: all 0.2s ease;
  outline: none;
  color: #374151;
  box-shadow: none;
  position: relative;
  width: 100%;
  box-sizing: border-box;

  @media (prefers-color-scheme: dark) {
    background: #1f2937;
    border-color: #4b5563;
    color: #f9fafb;
  }

  &:hover {
    border-color: #9ca3af;

    @media (prefers-color-scheme: dark) {
      border-color: #6b7280;
    }
  }

  &:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);

    @media (prefers-color-scheme: dark) {
      border-color: #6366f1;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }
  }

  &::placeholder {
    color: #9ca3af;
    font-weight: 400;
    opacity: 1;

    @media (prefers-color-scheme: dark) {
      color: #6b7280;
    }
  }

  @media (max-width: 640px) {
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
  }
}

.form-select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  text-align: left;

  @media (prefers-color-scheme: dark) {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }

  option {
    text-align: left;
    padding: 0.5rem;
  }
}

.form-error {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;

  @media (prefers-color-scheme: dark) {
    border-color: #f87171 !important;
    box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.2) !important;
  }
}

.error-text {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 0.25rem;

  @media (prefers-color-scheme: dark) {
    color: #f87171;
  }
}

/* 响应式优化 */
@media (max-width: 640px) {
  .form-select,
  .form-input {
    padding: 0.625rem;
    font-size: 0.8rem;
  }

  .form-label {
    font-size: 0.8rem;
  }

  .error-text {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .custom-form {
    gap: 0.75rem;
  }

  .form-field {
    gap: 0.25rem;
  }

  .form-select,
  .form-input {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .form-label {
    font-size: 0.75rem;
  }

  .error-text {
    font-size: 0.65rem;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .form-select,
  .form-input {
    border-width: 2px;
  }

  .form-label {
    font-weight: 600;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .form-select,
  .form-input {
    transition: none;
  }
}

/* 地址显示组件样式 */
.address-display {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;

  @media (prefers-color-scheme: dark) {
    background: #374151;
    border-color: #4b5563;
  }
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e5e7eb;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  @media (prefers-color-scheme: dark) {
    border-color: #4b5563;
  }
}

.address-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;

  @media (prefers-color-scheme: dark) {
    color: #9ca3af;
  }
}

.address-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  text-align: right;

  @media (prefers-color-scheme: dark) {
    color: #f9fafb;
  }
}

.edit-button {
  width: 100%;
  padding: 0.75rem;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1rem;

  &:hover {
    background: #f9fafb;
    border-color: #9ca3af;
  }

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  @media (prefers-color-scheme: dark) {
    background: #374151;
    border-color: #4b5563;
    color: #d1d5db;

    &:hover {
      background: #4b5563;
      border-color: #6b7280;
    }

    &:focus {
      border-color: #60a5fa;
      box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2);
    }
  }
}

/* 响应式优化 */
@media (max-width: 640px) {
  .address-display {
    padding: 0.75rem;
  }

  .address-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    padding: 0.375rem 0;
  }

  .address-value {
    text-align: left;
    font-weight: 500;
  }

  .edit-button {
    padding: 0.625rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .address-display {
    padding: 0.625rem;
  }

  .address-label,
  .address-value {
    font-size: 0.8rem;
  }

  .edit-button {
    padding: 0.5rem;
    font-size: 0.75rem;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .address-display {
    border-width: 2px;
  }

  .edit-button {
    border-width: 2px;
    font-weight: 600;
  }

  .address-label,
  .address-value {
    font-weight: 600;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .edit-button {
    transition: none;
  }
}