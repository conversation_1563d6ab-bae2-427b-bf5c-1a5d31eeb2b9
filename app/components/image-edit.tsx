import React, { useState, useRef, useCallback, useEffect } from "react";
import { IconButton } from "./button";
import { useNavigate } from "react-router-dom";
import { Path } from "../constant";
import styles from "./image-edit.module.scss";

import UploadIcon from "../icons/upload.svg";
import BackIcon from "../icons/back.svg";
import CloseIcon from "../icons/close.svg";

// 导入API和工具函数
import {
  uploadImageMultipart,
  fluxInpaintImage,
  fluxAnythingRemove,
  ImageType as ApiImageType,
  FluxInpaintImageRequest,
  FluxAnythingRemoveRequest,
} from "../api/custom-api/ai-tool-api";

// 导入共享的遮罩工具
import {
  base64ToFile,
  createBlackWhiteMask,
  createAlphaMaskImage,
  createMaskImage,
  setupDrawingTool,
  drawDot,
  safeToDataURL,
} from "../utils/mask-utils";
import {
  compressImage,
  IMAGE_COMPRESSION_PRESETS,
} from "../utils/image-compression-lib";
import { showToast } from "./ui-lib";

// 类型定义
type ImageType = "source" | "target";
type TabType = "immediate" | "result";
type ToolType = "brush" | "eraser";
type EditMode = "inpaint" | "remove"; // 重绘模式 | 消除模式

interface Point {
  x: number;
  y: number;
}

interface Material {
  id: number;
  image: string;
}

// 常量定义
const DEFAULT_BRUSH_SIZE = 32;
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "image/gif",
  "image/bmp",
  "image/tiff",
  "image/tif",
  "image/svg+xml",
  "image/avif",
  "image/heic",
  "image/heif",
  "image/ico",
  "image/x-icon",
  "image/vnd.microsoft.icon",
];
const PROCESSING_TIMEOUT = 30000; // 30秒

// 导入配置文件
import {
  ExampleImage,
  MaterialImage,
  IMAGE_REDRAW_EXAMPLES,
  IMAGE_REMOVE_EXAMPLES,
  MATERIAL_IMAGES,
  getImageRedrawExamplePaths,
  getImageRemoveExamplePaths,
  preloadImages,
} from "../config/images";

// 将配置文件中的素材转换为组件需要的格式
const MATERIALS: Material[] = MATERIAL_IMAGES.map((material, index) => ({
  id: material.id,
  image: material.path,
}));

// 根据编辑模式获取对应的案例
const getExamplesByMode = (mode: EditMode): ExampleImage[] => {
  return mode === "inpaint" ? IMAGE_REDRAW_EXAMPLES : IMAGE_REMOVE_EXAMPLES;
};

// 根据编辑模式获取案例图片路径
const getExamplePathsByMode = (mode: EditMode): string[] => {
  return mode === "inpaint"
    ? getImageRedrawExamplePaths()
    : getImageRemoveExamplePaths();
};

export function ImageEdit() {
  const navigate = useNavigate();

  // 主要状态
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [originalImage, setOriginalImage] = useState<string | null>(null); // 保存原始图片
  // 为每个模式单独保存预览图片
  const [inpaintPreviewImage, setInpaintPreviewImage] = useState<string | null>(
    null,
  ); // 重绘模式预览
  const [removePreviewImage, setRemovePreviewImage] = useState<string | null>(
    null,
  ); // 消除模式预览
  const [description, setDescription] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>("immediate");
  const [error, setError] = useState<string | null>(null);
  const [editMode, setEditMode] = useState<EditMode>("inpaint"); // 默认重绘模式
  const [isExampleSwitching, setIsExampleSwitching] = useState(false); // 案例切换动画状态

  // 任务处理相关状态
  const [taskId, setTaskId] = useState<string | null>(null);
  const [taskStatus, setTaskStatus] = useState<
    "waiting" | "processing" | "completed" | "failed" | null
  >(null);
  const [originalImageFile, setOriginalImageFile] = useState<File | null>(null);
  const [maskImageFile, setMaskImageFile] = useState<File | null>(null);

  // 局部重绘相关状态
  const [maskCanvas, setMaskCanvas] = useState<string | null>(null);
  const [showInpaintModal, setShowInpaintModal] = useState(false);
  const [currentEditingImage, setCurrentEditingImage] =
    useState<ImageType | null>(null);
  const [brushSize, setBrushSize] = useState(DEFAULT_BRUSH_SIZE);
  const [isDrawing, setIsDrawing] = useState(false);
  const [maskHistory, setMaskHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [currentTool, setCurrentTool] = useState<ToolType>("brush");
  const [lastPoint, setLastPoint] = useState<Point | null>(null);
  const [hasMoved, setHasMoved] = useState(false); // 跟踪是否有移动

  // 为每个模式单独保存涂抹状态
  const [inpaintMaskData, setInpaintMaskData] = useState<string | null>(null); // 重绘模式的遮罩
  const [removeMaskData, setRemoveMaskData] = useState<string | null>(null); // 消除模式的遮罩

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  // 工具函数
  const validateFile = useCallback((file: File): string | null => {
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return `不支持的文件类型。请选择 ${ALLOWED_FILE_TYPES.join(
        ", ",
      )} 格式的图片。`;
    }
    if (file.size > MAX_FILE_SIZE) {
      return `文件大小超过限制。请选择小于 ${
        MAX_FILE_SIZE / 1024 / 1024
      }MB 的图片。`;
    }
    return null;
  }, []);

  // 坐标转换函数
  const getCanvasCoordinates = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const canvas = canvasRef.current;
      if (!canvas) return { x: 0, y: 0 };

      const rect = canvas.getBoundingClientRect();
      const scaleX = canvas.width / rect.width;
      const scaleY = canvas.height / rect.height;

      const x = (e.clientX - rect.left) * scaleX;
      const y = (e.clientY - rect.top) * scaleY;

      return { x, y };
    },
    [],
  );

  // 处理模式切换，包含动画效果
  const handleModeChange = useCallback(
    (newMode: EditMode) => {
      if (newMode === editMode) return;

      setIsExampleSwitching(true);

      // 延迟切换模式，让动画先开始
      setTimeout(() => {
        setEditMode(newMode);

        // 恢复到原始图片，避免模式间相互影响
        if (originalImage) {
          setUploadedImage(originalImage);
          // 清空所有预览图片
          setInpaintPreviewImage(null);
          setRemovePreviewImage(null);

          // 强制清空canvas
          const canvas = canvasRef.current;
          if (canvas) {
            const ctx = canvas.getContext("2d");
            if (ctx) {
              ctx.clearRect(0, 0, canvas.width, canvas.height);
            }
          }

          // 强制关闭编辑器，确保图片重新加载
          if (showInpaintModal) {
            setShowInpaintModal(false);
            setCurrentEditingImage(null);
          }

          // 清空所有涂抹相关状态
          setMaskHistory([]);
          setHistoryIndex(-1);
          setIsDrawing(false);
          setLastPoint(null);
          setHasMoved(false);

          // 重置当前模式的遮罩状态
          if (newMode === "inpaint") {
            setInpaintMaskData(null);
          } else {
            setRemoveMaskData(null);
          }

          // 重置描述文本（切换到消除模式时清空描述）
          if (newMode === "remove") {
            setDescription("");
          }

          // 延迟重新打开编辑器，确保图片已经更新
          setTimeout(() => {
            if (originalImage) {
              // 再次确认原图存在
              setCurrentEditingImage("source");
              setShowInpaintModal(true);

              // 再次延迟确保canvas被正确清空
              setTimeout(() => {
                const canvas = canvasRef.current;
                if (canvas) {
                  const ctx = canvas.getContext("2d");
                  if (ctx) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    // 重置历史记录
                    const initialState = canvas.toDataURL();
                    setMaskHistory([initialState]);
                    setHistoryIndex(0);
                  }
                }
              }, 100);
            }
          }, 300);
        }

        // 动画完成后恢复状态
        setTimeout(() => {
          setIsExampleSwitching(false);
        }, 150);
      }, 150);
    },
    [editMode, originalImage, showInpaintModal],
  );

  // 使用共享的遮罩工具（已移除重复实现）

  // 使用共享的遮罩工具（已移除重复实现）

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  // 预加载案例图片 - 根据模式变化重新加载
  useEffect(() => {
    const imagePaths = getExamplePathsByMode(editMode);
    preloadImages(imagePaths).catch((error) => {
      console.warn("Failed to preload example images:", error);
    });
  }, [editMode]);

  const showError = useCallback((message: string) => {
    setError(message);
    setTimeout(() => setError(null), 5000); // 5秒后自动清除错误
  }, []);

  const handleImageUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // 验证文件
      const validationError = validateFile(file);
      if (validationError) {
        showError(validationError);
        return;
      }

      resetError();

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const imageUrl = e.target?.result as string;
          if (!imageUrl) {
            throw new Error("无法读取图片文件");
          }

          setUploadedImage(imageUrl);
          setOriginalImage(imageUrl); // 保存原始图片
          setOriginalImageFile(file); // 保存原始文件

          // 重置所有编辑状态，确保重新上传时状态干净
          setMaskCanvas(null);
          setDescription("");
          setInpaintMaskData(null);
          setRemoveMaskData(null);
          setInpaintPreviewImage(null);
          setRemovePreviewImage(null);

          // 如果编辑器是打开的，关闭它
          if (showInpaintModal) {
            setShowInpaintModal(false);
            setCurrentEditingImage(null);
          }

          // 关闭编辑器如果正在打开
          if (showInpaintModal) {
            setShowInpaintModal(false);
            setCurrentEditingImage(null);
          }

          // 重置绘制状态
          setIsDrawing(false);
          setLastPoint(null);
          setMaskHistory([]);
          setHistoryIndex(-1);
        } catch (err) {
          showError("图片加载失败，请重试");
          console.error("Image upload error:", err);
        }
      };

      reader.onerror = () => {
        showError("文件读取失败，请重试");
      };

      reader.readAsDataURL(file);
    },
    [validateFile, showError, resetError, showInpaintModal],
  );

  // 删除图片
  const removeImage = useCallback(() => {
    try {
      resetError();
      setUploadedImage(null);
      setOriginalImage(null);
      setInpaintPreviewImage(null);
      setRemovePreviewImage(null);
      setOriginalImageFile(null);
      setMaskImageFile(null);

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }

      // 关闭编辑器如果正在打开
      if (showInpaintModal) {
        setShowInpaintModal(false);
        setCurrentEditingImage(null);
      }

      // 重置绘制状态
      setIsDrawing(false);
      setLastPoint(null);
      setMaskHistory([]);
      setHistoryIndex(-1);

      // 重置任务状态
      setTaskId(null);
      setTaskStatus(null);
    } catch (err) {
      console.error("Remove image error:", err);
      showError("删除图片时出错");
    }
  }, [resetError, showError, showInpaintModal]);

  // 打开局部重绘弹框
  const handleOpenInpaint = useCallback(() => {
    try {
      resetError();

      if (!uploadedImage) {
        showError("请先上传图片");
        return;
      }

      // 重置所有绘制相关状态
      setIsDrawing(false);
      setLastPoint(null);
      setMaskHistory([]);
      setHistoryIndex(-1);
      setBrushSize(DEFAULT_BRUSH_SIZE);
      setCurrentTool("brush");

      setCurrentEditingImage("source");
      setShowInpaintModal(true);
    } catch (err) {
      console.error("Open inpaint modal error:", err);
      showError("打开编辑器失败");
    }
  }, [resetError, showError, uploadedImage]);

  // 关闭局部重绘弹框
  const handleCloseInpaint = useCallback(() => {
    try {
      setShowInpaintModal(false);
      setCurrentEditingImage(null);
      // 清理绘制状态
      setIsDrawing(false);
      setLastPoint(null);
    } catch (err) {
      console.error("Close inpaint modal error:", err);
    }
  }, []);

  // 初始化画布
  const initializeCanvas = useCallback(() => {
    try {
      const canvas = canvasRef.current;
      const image = imageRef.current;

      if (!canvas || !image) {
        console.warn("Canvas or image not available for initialization");
        return;
      }

      // 等待图片完全加载
      if (!image.complete || image.naturalWidth === 0) {
        console.warn("Image not fully loaded, retrying...");
        setTimeout(initializeCanvas, 100);
        return;
      }

      const ctx = canvas.getContext("2d");
      if (!ctx) {
        console.error("Failed to get canvas context");
        showError("画布初始化失败");
        return;
      }

      // 获取图片尺寸
      const imageWidth = image.naturalWidth || image.width;
      const imageHeight = image.naturalHeight || image.height;

      if (imageWidth === 0 || imageHeight === 0) {
        console.error("Invalid image dimensions:", imageWidth, imageHeight);
        showError("图片尺寸无效");
        return;
      }

      // 设置画布尺寸与图片相同
      canvas.width = imageWidth;
      canvas.height = imageHeight;

      // 设置画布样式尺寸
      const rect = image.getBoundingClientRect();
      if (rect.width > 0 && rect.height > 0) {
        canvas.style.width = rect.width + "px";
        canvas.style.height = rect.height + "px";
      }

      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 重置绘制状态
      setIsDrawing(false);
      setLastPoint(null);

      // 保存初始状态到历史记录
      const initialState = canvas.toDataURL();
      setMaskHistory([initialState]);
      setHistoryIndex(0);

      // 重置绘制状态，确保状态一致性
      setIsDrawing(false);
      setLastPoint(null);

      console.log(
        "Canvas initialized successfully:",
        canvas.width,
        "x",
        canvas.height,
      );
    } catch (err) {
      console.error("Canvas initialization error:", err);
      showError("画布初始化失败");
    }
  }, [showError]);

  // 开始绘制
  const startDrawing = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      const { x, y } = getCanvasCoordinates(e);

      setIsDrawing(true);
      setLastPoint({ x, y });
      setHasMoved(false); // 重置移动状态

      // 使用共享的绘制工具设置
      setupDrawingTool(ctx, currentTool, brushSize);

      // 开始新的路径
      ctx.beginPath();
      ctx.moveTo(x, y);

      // 不在开始时绘制点，避免出现大圆形
      // 只有在鼠标释放时没有移动才绘制单点
    },
    [brushSize, currentTool, getCanvasCoordinates],
  );

  // 绘制
  const draw = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      if (!isDrawing || !lastPoint) return;

      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      const { x, y } = getCanvasCoordinates(e);

      // 计算距离，避免绘制过于密集的点
      const distance = Math.sqrt(
        Math.pow(x - lastPoint.x, 2) + Math.pow(y - lastPoint.y, 2),
      );
      if (distance < 2) return; // 如果移动距离太小，跳过绘制

      // 标记已移动
      setHasMoved(true);

      // 继续当前路径
      ctx.lineTo(x, y);
      ctx.stroke();

      // 更新最后一个点
      setLastPoint({ x, y });
    },
    [isDrawing, lastPoint, getCanvasCoordinates],
  );

  // 停止绘制
  const stopDrawing = useCallback(() => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext("2d");
      if (ctx && lastPoint && !hasMoved) {
        // 只有在没有移动的情况下才绘制单点
        drawDot(ctx, currentTool, lastPoint.x, lastPoint.y, brushSize);
      }
    }

    setIsDrawing(false);
    setLastPoint(null);
    setHasMoved(false); // 重置移动状态

    // 保存当前状态到历史记录
    if (canvas) {
      const currentState = canvas.toDataURL();
      const newHistory = maskHistory.slice(0, historyIndex + 1);
      newHistory.push(currentState);
      setMaskHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);

      // 保存当前模式的遮罩数据
      if (editMode === "inpaint") {
        setInpaintMaskData(currentState);
      } else {
        setRemoveMaskData(currentState);
      }
    }
  }, [
    isDrawing,
    lastPoint,
    hasMoved,
    brushSize,
    maskHistory,
    historyIndex,
    currentTool,
  ]);

  // 撤销
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);

      const canvas = canvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext("2d");
        if (ctx) {
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
          };
          img.src = maskHistory[newIndex];
        }
      }
    }
  }, [historyIndex, maskHistory]);

  // 重做
  const handleRedo = useCallback(() => {
    if (historyIndex < maskHistory.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);

      const canvas = canvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext("2d");
        if (ctx) {
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
          };
          img.src = maskHistory[newIndex];
        }
      }
    }
  }, [historyIndex, maskHistory]);

  // 重置画布
  const handleReset = useCallback(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        const initialState = canvas.toDataURL();
        setMaskHistory([initialState]);
        setHistoryIndex(0);
      }
    }
  }, []);

  // 确认局部重绘
  const handleConfirmInpaint = useCallback(() => {
    try {
      const canvas = canvasRef.current;
      const image = imageRef.current;

      if (!canvas || !image || !currentEditingImage) {
        console.error("Canvas, image or currentEditingImage not found");
        showError("编辑器状态异常，请重新打开");
        return;
      }

      // 验证画布和图片尺寸
      if (canvas.width === 0 || canvas.height === 0) {
        console.error("Canvas has invalid dimensions");
        showError("画布尺寸异常，请重新打开编辑器");
        return;
      }

      // 创建一个新的canvas来合成最终图片
      const finalCanvas = document.createElement("canvas");
      const finalCtx = finalCanvas.getContext("2d");

      if (!finalCtx) {
        console.error("Failed to get canvas context");
        showError("无法创建画布上下文");
        return;
      }

      // 设置最终canvas的尺寸
      const imageWidth = image.naturalWidth || image.width;
      const imageHeight = image.naturalHeight || image.height;

      if (imageWidth === 0 || imageHeight === 0) {
        console.error("Image has invalid dimensions");
        showError("图片尺寸异常");
        return;
      }

      finalCanvas.width = imageWidth;
      finalCanvas.height = imageHeight;

      // 根据编辑模式生成不同的预览效果
      // 1. 先绘制原始图片
      finalCtx.drawImage(image, 0, 0);

      // 2. 创建黑白遮罩
      const blackWhiteMask = createBlackWhiteMask(canvas);

      if (editMode === "remove") {
        // 消除模式：清除涂抹区域（与万物替换逻辑一致）
        finalCtx.globalCompositeOperation = "destination-out";

        // 创建临时画布，只保留白色区域用于清除
        const tempCanvas = document.createElement("canvas");
        const tempCtx = tempCanvas.getContext("2d");
        if (tempCtx) {
          tempCanvas.width = blackWhiteMask.width;
          tempCanvas.height = blackWhiteMask.height;

          // 绘制黑白遮罩
          tempCtx.drawImage(blackWhiteMask, 0, 0);

          // 只保留白色区域（涂抹区域）
          const imageData = tempCtx.getImageData(
            0,
            0,
            tempCanvas.width,
            tempCanvas.height,
          );
          const data = imageData.data;

          for (let i = 0; i < data.length; i += 4) {
            if (data[i] > 128) {
              // 白色像素（涂抹区域）
              data[i] = 255; // R
              data[i + 1] = 255; // G
              data[i + 2] = 255; // B
              data[i + 3] = 255; // A
            } else {
              data[i] = 0; // R
              data[i + 1] = 0; // G
              data[i + 2] = 0; // B
              data[i + 3] = 0; // A (透明)
            }
          }

          tempCtx.putImageData(imageData, 0, 0);
          finalCtx.drawImage(tempCanvas, 0, 0);
        }
      } else {
        // 重绘模式：也使用destination-out清除涂抹区域（与万物替换逻辑完全一致）
        // 这样用户可以看到要重绘的区域被清除的效果
        finalCtx.globalCompositeOperation = "destination-out";

        // 创建临时画布，只保留白色区域用于清除
        const tempCanvas = document.createElement("canvas");
        const tempCtx = tempCanvas.getContext("2d");
        if (tempCtx) {
          tempCanvas.width = blackWhiteMask.width;
          tempCanvas.height = blackWhiteMask.height;

          // 绘制黑白遮罩
          tempCtx.drawImage(blackWhiteMask, 0, 0);

          // 只保留白色区域（涂抹区域）
          const imageData = tempCtx.getImageData(
            0,
            0,
            tempCanvas.width,
            tempCanvas.height,
          );
          const data = imageData.data;

          for (let i = 0; i < data.length; i += 4) {
            if (data[i] > 128) {
              // 白色像素（涂抹区域）
              data[i] = 255; // R
              data[i + 1] = 255; // G
              data[i + 2] = 255; // B
              data[i + 3] = 255; // A
            } else {
              data[i] = 0; // R
              data[i + 1] = 0; // G
              data[i + 2] = 0; // B
              data[i + 3] = 0; // A (透明)
            }
          }

          tempCtx.putImageData(imageData, 0, 0);
          finalCtx.drawImage(tempCanvas, 0, 0);
        }
      }

      // 保存遮罩用于API调用（与万物替换逻辑一致）
      try {
        // 创建黑色遮罩（涂抹区域为黑色，未涂抹区域为透明）
        const blackMaskCanvas = document.createElement("canvas");
        const blackMaskCtx = blackMaskCanvas.getContext("2d");

        if (blackMaskCtx) {
          blackMaskCanvas.width = blackWhiteMask.width;
          blackMaskCanvas.height = blackWhiteMask.height;

          // 获取黑白遮罩数据
          const maskImageData = blackWhiteMask
            .getContext("2d")
            ?.getImageData(0, 0, blackWhiteMask.width, blackWhiteMask.height);
          if (maskImageData) {
            const blackMaskData = blackMaskCtx.createImageData(
              blackWhiteMask.width,
              blackWhiteMask.height,
            );
            const srcData = maskImageData.data;
            const blackData = blackMaskData.data;

            for (let i = 0; i < srcData.length; i += 4) {
              if (srcData[i] > 128) {
                // 白色像素（涂抹区域）
                // 转换为黑色
                blackData[i] = 0; // R = 0 (黑色)
                blackData[i + 1] = 0; // G = 0 (黑色)
                blackData[i + 2] = 0; // B = 0 (黑色)
                blackData[i + 3] = 255; // A = 255 (完全不透明)
              } else {
                // 未涂抹区域设为透明
                blackData[i] = 0; // R = 0
                blackData[i + 1] = 0; // G = 0
                blackData[i + 2] = 0; // B = 0
                blackData[i + 3] = 0; // A = 0 (透明)
              }
            }

            blackMaskCtx.putImageData(blackMaskData, 0, 0);

            const maskDataUrl = blackMaskCanvas.toDataURL("image/png");
            if (
              maskDataUrl &&
              maskDataUrl.startsWith("data:") &&
              maskDataUrl.includes(",")
            ) {
              setMaskCanvas(maskDataUrl);
            } else {
              console.error("Invalid mask data URL generated:", maskDataUrl);
              setMaskCanvas(null);
            }
          }
        }
      } catch (error) {
        console.error("Error generating mask data URL:", error);
        setMaskCanvas(null);
      }

      // 将合成后的图片转换为base64并更新图片状态
      let finalImageData: string;
      try {
        finalImageData = finalCanvas.toDataURL("image/png");
        // 验证生成的data URL
        if (
          !finalImageData ||
          !finalImageData.startsWith("data:") ||
          !finalImageData.includes(",")
        ) {
          throw new Error("Invalid data URL generated");
        }
      } catch (securityError) {
        console.warn(
          "Canvas tainted, using alternative method:",
          securityError,
        );
        // 如果Canvas被污染，创建一个新的Canvas来重新绘制
        const cleanCanvas = document.createElement("canvas");
        const cleanCtx = cleanCanvas.getContext("2d");

        if (cleanCtx) {
          cleanCanvas.width = finalCanvas.width;
          cleanCanvas.height = finalCanvas.height;

          // 创建一个新的Image对象来重新加载原始图片
          const newImage = new Image();
          newImage.crossOrigin = "anonymous";

          newImage.onload = () => {
            try {
              // 绘制原始图片
              cleanCtx.drawImage(newImage, 0, 0);

              // 根据编辑模式应用不同的遮罩效果
              const newBlackWhiteMask = createBlackWhiteMask(canvas);

              if (editMode === "remove") {
                // 消除模式：清除涂抹区域
                cleanCtx.globalCompositeOperation = "destination-out";

                // 创建临时画布，只保留白色区域用于清除
                const tempCanvas = document.createElement("canvas");
                const tempCtx = tempCanvas.getContext("2d");
                if (tempCtx) {
                  tempCanvas.width = newBlackWhiteMask.width;
                  tempCanvas.height = newBlackWhiteMask.height;

                  // 绘制黑白遮罩
                  tempCtx.drawImage(newBlackWhiteMask, 0, 0);

                  // 只保留白色区域（涂抹区域）
                  const imageData = tempCtx.getImageData(
                    0,
                    0,
                    tempCanvas.width,
                    tempCanvas.height,
                  );
                  const data = imageData.data;

                  for (let i = 0; i < data.length; i += 4) {
                    if (data[i] > 128) {
                      // 白色像素（涂抹区域）
                      data[i] = 255; // R
                      data[i + 1] = 255; // G
                      data[i + 2] = 255; // B
                      data[i + 3] = 255; // A
                    } else {
                      data[i] = 0; // R
                      data[i + 1] = 0; // G
                      data[i + 2] = 0; // B
                      data[i + 3] = 0; // A (透明)
                    }
                  }

                  tempCtx.putImageData(imageData, 0, 0);
                  cleanCtx.drawImage(tempCanvas, 0, 0);
                }
              } else {
                // 重绘模式：也使用destination-out清除涂抹区域（与万物替换逻辑完全一致）
                cleanCtx.globalCompositeOperation = "destination-out";

                // 创建临时画布，只保留白色区域用于清除
                const tempCanvas = document.createElement("canvas");
                const tempCtx = tempCanvas.getContext("2d");
                if (tempCtx) {
                  tempCanvas.width = newBlackWhiteMask.width;
                  tempCanvas.height = newBlackWhiteMask.height;

                  // 绘制黑白遮罩
                  tempCtx.drawImage(newBlackWhiteMask, 0, 0);

                  // 只保留白色区域（涂抹区域）
                  const imageData = tempCtx.getImageData(
                    0,
                    0,
                    tempCanvas.width,
                    tempCanvas.height,
                  );
                  const data = imageData.data;

                  for (let i = 0; i < data.length; i += 4) {
                    if (data[i] > 128) {
                      // 白色像素（涂抹区域）
                      data[i] = 255; // R
                      data[i + 1] = 255; // G
                      data[i + 2] = 255; // B
                      data[i + 3] = 255; // A
                    } else {
                      data[i] = 0; // R
                      data[i + 1] = 0; // G
                      data[i + 2] = 0; // B
                      data[i + 3] = 0; // A (透明)
                    }
                  }

                  tempCtx.putImageData(imageData, 0, 0);
                  cleanCtx.drawImage(tempCanvas, 0, 0);
                }
              }

              // 尝试导出
              const cleanImageData = cleanCanvas.toDataURL("image/png");
              // 根据当前模式设置对应的预览图片
              if (editMode === "inpaint") {
                setInpaintPreviewImage(cleanImageData);
              } else {
                setRemovePreviewImage(cleanImageData);
              }

              // 重置状态
              setIsDrawing(false);
              setLastPoint(null);
              setMaskHistory([]);
              setHistoryIndex(-1);
              setShowInpaintModal(false);
              setCurrentEditingImage(null);

              console.log("局部重绘完成，图片已更新（使用清洁Canvas）");
            } catch (err) {
              console.error("Clean canvas export failed:", err);
              showError("图片导出失败，请重试");
            }
          };

          newImage.onerror = () => {
            console.error("Failed to reload image for clean canvas");
            showError("图片重新加载失败，请重试");
          };

          newImage.src = uploadedImage!;
          return; // 异步处理，直接返回
        } else {
          throw new Error("无法创建清洁Canvas上下文");
        }
      }

      // 根据当前模式更新对应的预览图片，不影响原始上传图片
      if (editMode === "inpaint") {
        setInpaintPreviewImage(finalImageData);
      } else {
        setRemovePreviewImage(finalImageData);
      }

      // 保存遮罩图片文件
      const maskFile = createMaskImage(canvas);
      console.log("maskFile", maskFile);
      setMaskImageFile(maskFile);

      // 重置绘制状态
      setIsDrawing(false);
      setLastPoint(null);
      setMaskHistory([]);
      setHistoryIndex(-1);

      // 关闭弹框
      setShowInpaintModal(false);
      setCurrentEditingImage(null);

      console.log("局部重绘完成，图片已更新");
    } catch (err) {
      console.error("Confirm inpaint error:", err);
      showError("确认修改时出错，请重试");
    }
  }, [currentEditingImage, showError]);

  // 准备文件的辅助函数
  const prepareFiles = useCallback(async () => {
    let originalFile: File;
    let maskFile: File | null = null;

    // 准备原图文件
    if (originalImageFile) {
      originalFile = originalImageFile;
    } else {
      originalFile = base64ToFile(uploadedImage!, "original.png");
    }

    // 准备遮罩文件
    if (maskImageFile) {
      maskFile = maskImageFile;
    } else if (canvasRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext("2d");
      if (ctx) {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const hasContent = imageData.data.some(
          (value, index) => index % 4 === 3 && value > 0,
        );
        if (hasContent) {
          maskFile = createMaskImage(canvas);
        }
      }
    }

    return { originalFile, maskFile };
  }, [
    originalImageFile,
    uploadedImage,
    maskImageFile,
    base64ToFile,
    createMaskImage,
  ]);

  // 并发压缩和上传图片的辅助函数
  const compressAndUploadImages = useCallback(
    async (originalFile: File, maskFile: File | null) => {
      // 并发压缩图片
      const compressionTasks = [
        compressImage(originalFile, IMAGE_COMPRESSION_PRESETS.STANDARD),
      ];

      if (maskFile) {
        compressionTasks.push(
          compressImage(maskFile, IMAGE_COMPRESSION_PRESETS.STANDARD),
        );
      }

      const compressionResults = await Promise.all(compressionTasks);
      const compressedOriginal = compressionResults[0];
      const compressedMask = compressionResults[1] || null;

      // 并发上传图片
      const uploadTasks = [
        uploadImageMultipart({
          uploadImage: compressedOriginal.file,
          imageType: ApiImageType.ORIGINAL,
          imageFormat: compressedOriginal.file.type.split("/")[1] || "png",
        }),
      ];

      if (compressedMask) {
        uploadTasks.push(
          uploadImageMultipart({
            uploadImage: compressedMask.file,
            imageType: ApiImageType.MASK,
            imageFormat: compressedMask.file.type.split("/")[1] || "png",
          }),
        );
      }

      const uploadResults = await Promise.all(uploadTasks);
      return {
        originalResult: uploadResults[0],
        maskResult: uploadResults[1] || null,
      };
    },
    [],
  );

  // 提交重绘任务的辅助函数
  const submitInpaintTask = useCallback(
    async (
      uploadResults: { originalResult: any; maskResult: any },
      prompt: string,
    ) => {
      if (editMode === "remove") {
        // 消除模式：使用万物删除接口
        const removeRequest: FluxAnythingRemoveRequest = {
          originalImageName: uploadResults.originalResult.name,
          maskImageName: uploadResults.maskResult?.name || "",
        };
        return await fluxAnythingRemove(removeRequest);
      } else {
        // 重绘模式：使用图片重绘接口
        const inpaintRequest: FluxInpaintImageRequest = {
          textPrompt: prompt,
          originalImageName: uploadResults.originalResult.name,
          maskImageName: uploadResults.maskResult?.name || "",
        };
        return await fluxInpaintImage(inpaintRequest);
      }
    },
    [editMode],
  );

  const handleProcess = useCallback(async () => {
    if (!uploadedImage) {
      showError("请先上传图片");
      return;
    }

    // 检查当前模式是否有涂抹内容
    const currentMaskData =
      editMode === "inpaint" ? inpaintMaskData : removeMaskData;

    if (!currentMaskData) {
      showError(
        editMode === "inpaint"
          ? "请先涂抹需要重绘的区域"
          : "请先涂抹需要消除的区域",
      );
      return;
    }

    // 重绘模式需要描述，消除模式不需要
    const finalDescription =
      editMode === "inpaint" ? description.trim() || "图像编辑处理" : "";

    // 跳转到生成结果tab页
    setActiveTab("result");
    setIsProcessing(true);
    setTaskStatus("processing");
    resetError();

    try {
      // 准备文件
      const { originalFile, maskFile } = await prepareFiles();

      // 并发压缩和上传图片
      const uploadResults = await compressAndUploadImages(
        originalFile,
        maskFile,
      );

      // 提交重绘任务
      const inpaintResult = await submitInpaintTask(
        uploadResults,
        finalDescription,
      );

      // 更新状态
      setTaskId(inpaintResult.prompt_id);
      setTaskStatus("processing");
    } catch (error) {
      console.error("处理失败:", error);
      // 处理失败时清除任务状态，回到初始模式
      setTaskStatus(null);
      setTaskId(null);
      showError(
        error instanceof Error ? error.message : "处理失败，请稍后重试",
      );
      setActiveTab("immediate");
    } finally {
      setIsProcessing(false);
    }
  }, [
    uploadedImage,
    description,
    showError,
    resetError,
    prepareFiles,
    compressAndUploadImages,
    submitInpaintTask,
    editMode,
    inpaintMaskData,
    removeMaskData,
  ]);

  const canProcess = uploadedImage && !isProcessing;

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      // 清理错误状态
      setError(null);
      // 清理处理状态
      setIsProcessing(false);
      // 清理绘制状态
      setIsDrawing(false);
      setLastPoint(null);
    };
  }, []);

  return (
    <React.Fragment>
      <div className={styles.container}>
        {/* 头部导航 */}
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <IconButton
              icon={<BackIcon />}
              text="返回"
              onClick={() => navigate(-1)}
              className={styles.backButton}
            />
            <h1 className={styles.title}>图像编辑</h1>
          </div>
          <div className={styles.headerRight}>
            <button
              className={styles.myWorksButton}
              onClick={() => navigate(Path.MyWorks)}
            >
              我的作品
            </button>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className={styles.errorMessage}>
            <span>{error}</span>
            <button onClick={resetError} className={styles.errorClose}>
              ×
            </button>
          </div>
        )}

        {/* 主要内容区域 */}
        <div className={styles.mainContainer}>
          {/* 左侧案例展示区 - 占更大宽度 */}
          <div className={styles.exampleArea}>
            {/* 案例展示 */}
            <div className={styles.exampleSection}>
              <h3 className={isExampleSwitching ? styles.switching : ""}>
                {editMode === "inpaint" ? "重绘案例" : "消除案例"}
              </h3>
              <div
                className={`${styles.exampleContainer} ${
                  isExampleSwitching ? styles.switching : ""
                }`}
              >
                {getExamplesByMode(editMode).map((example) => (
                  <div key={example.id} className={styles.exampleItem}>
                    <div className={styles.exampleImages}>
                      <div className={styles.beforeAfter}>
                        <div className={styles.imageContainer}>
                          <img src={example.beforeImage} alt="处理前" />
                          <span className={styles.imageLabel}>原图</span>
                        </div>
                        <div className={styles.imageContainer}>
                          <img src={example.afterImage} alt="处理后" />
                          <span className={styles.imageLabel}>效果</span>
                        </div>
                        <div className={styles.arrow}>→</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 素材展示 */}
            <div className={styles.materialSection}>
              <h3>素材</h3>
              <div className={styles.materialGrid}>
                {MATERIALS.map((material) => (
                  <div
                    key={material.id}
                    className={styles.materialItem}
                    onClick={async () => {
                      try {
                        setUploadedImage(material.image);

                        // 将素材图片转换为File对象
                        const response = await fetch(material.image);
                        const blob = await response.blob();
                        const file = new File(
                          [blob],
                          `material-${material.id}.png`,
                          { type: "image/png" },
                        );
                        setOriginalImageFile(file);

                        // 重置遮罩相关状态
                        setMaskImageFile(null);
                        setInpaintMaskData(null);
                        setRemoveMaskData(null);

                        // 清除预览图片
                        setInpaintPreviewImage(null);
                        setRemovePreviewImage(null);

                        // 关闭编辑器如果正在打开
                        if (showInpaintModal) {
                          setShowInpaintModal(false);
                          setCurrentEditingImage(null);
                        }

                        // 重置绘制状态
                        setIsDrawing(false);
                        setLastPoint(null);
                        setMaskHistory([]);
                        setHistoryIndex(-1);

                        // 重置任务状态
                        setTaskId(null);
                        setTaskStatus(null);

                        // 如果编辑器是打开的，强制刷新图片和重新初始化画布
                        if (showInpaintModal) {
                          // 强制刷新图片
                          const img = imageRef.current;
                          if (img) {
                            const currentSrc = img.src;
                            img.src = "";
                            setTimeout(() => {
                              img.src = currentSrc;
                            }, 10);
                          }
                        }
                      } catch (error) {
                        console.error("素材加载失败:", error);
                        showError("素材加载失败，请重试");
                      }
                    }}
                  >
                    <div className={styles.materialImageWrapper}>
                      <img src={material.image} alt="素材" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 右侧操作区 - 相对较小 */}
          <div className={styles.operationArea}>
            <div className={styles.editSection}>
              {/* 统一的内容容器 */}
              <div className={styles.contentContainer}>
                {/* Tab导航 */}
                <div className={styles.tabNavigation}>
                  <button
                    className={`${styles.tab} ${
                      activeTab === "immediate" ? styles.activeTab : ""
                    }`}
                    onClick={() => setActiveTab("immediate")}
                  >
                    立即使用
                  </button>
                  <button
                    className={`${styles.tab} ${
                      activeTab === "result" ? styles.activeTab : ""
                    }`}
                    onClick={() => setActiveTab("result")}
                  >
                    生成结果
                  </button>
                </div>

                {/* Tab内容 */}
                <div className={styles.tabContent}>
                  {activeTab === "immediate" && (
                    <div className={styles.immediateTab}>
                      {/* 编辑模式选择 - 放在最顶部 */}
                      <div className={styles.modeSection}>
                        <div className={styles.modeSelector}>
                          <button
                            className={`${styles.modeButton} ${
                              editMode === "inpaint" ? styles.active : ""
                            }`}
                            onClick={() => handleModeChange("inpaint")}
                          >
                            <svg
                              width="18"
                              height="18"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                            >
                              <path d="m9.06 11.9 8.07-8.06a2.85 2.85 0 1 1 4.03 4.03l-8.06 8.08" />
                              <path d="M7.07 14.94c-1.66 0-3 1.35-3 3.02 0 1.33-2.5 1.52-2 2.02 1.08-2 2.74-2 4-2Z" />
                            </svg>
                            <span>局部重绘</span>
                          </button>
                          <button
                            className={`${styles.modeButton} ${
                              editMode === "remove" ? styles.active : ""
                            }`}
                            onClick={() => handleModeChange("remove")}
                          >
                            <svg
                              width="18"
                              height="18"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                            >
                              <path d="M3 6h18" />
                              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                            </svg>
                            <span>局部消除</span>
                          </button>
                        </div>
                      </div>

                      {/* 图片上传区域 */}
                      <div className={styles.uploadSection}>
                        <label className={styles.uploadLabel}>
                          上传图片<span className={styles.required}>*</span>
                        </label>
                        <div
                          className={styles.uploadArea}
                          onClick={
                            uploadedImage
                              ? undefined
                              : () => fileInputRef.current?.click()
                          }
                        >
                          {uploadedImage ? (
                            <div className={styles.imagePreview}>
                              <img
                                key={uploadedImage} // 强制重新渲染
                                src={
                                  editMode === "inpaint"
                                    ? inpaintPreviewImage || uploadedImage
                                    : removePreviewImage || uploadedImage
                                }
                                alt="上传的图片"
                              />
                              <div className={styles.imageControls}>
                                <button
                                  className={`${styles.controlButton} ${styles.inpaintButton}`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleOpenInpaint();
                                  }}
                                  title={
                                    editMode === "inpaint"
                                      ? "局部重绘"
                                      : "局部消除"
                                  }
                                >
                                  {editMode === "inpaint" ? (
                                    <svg
                                      width="14"
                                      height="14"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    >
                                      <path d="m9.06 11.9 8.07-8.06a2.85 2.85 0 1 1 4.03 4.03l-8.06 8.08" />
                                      <path d="M7.07 14.94c-1.66 0-3 1.35-3 3.02 0 1.33-2.5 1.52-2 2.02 1.08-2 2.74-2 4-2Z" />
                                      <path d="M14 6 8.5 11.5" />
                                      <path d="M10.5 9 15 4.5" />
                                    </svg>
                                  ) : (
                                    <svg
                                      width="14"
                                      height="14"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                    >
                                      <path d="M3 6h18" />
                                      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                                      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                                    </svg>
                                  )}
                                  {editMode === "inpaint"
                                    ? "局部重绘"
                                    : "局部消除"}
                                </button>
                                <button
                                  className={`${styles.controlButton} ${styles.deleteButton}`}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    removeImage();
                                  }}
                                  title="删除图片"
                                >
                                  <CloseIcon />
                                </button>
                              </div>
                            </div>
                          ) : (
                            <div className={styles.uploadPlaceholder}>
                              <div className={styles.uploadIcon}>
                                <UploadIcon />
                              </div>
                              <div className={styles.uploadText}>
                                拖拽文件到这里或点击上传
                              </div>
                            </div>
                          )}
                        </div>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          style={{ display: "none" }}
                        />
                      </div>

                      {/* 描述输入区域 - 仅在重绘模式下显示 */}
                      {editMode === "inpaint" && (
                        <div className={styles.uploadSection}>
                          <label className={styles.uploadLabel}>
                            描述信息（可选）
                            <span
                              className={`${styles.charCount} ${
                                description.length > 450
                                  ? styles.error
                                  : description.length > 400
                                  ? styles.warning
                                  : ""
                              }`}
                            >
                              {description.length}/500
                            </span>
                          </label>
                          <textarea
                            className={styles.descriptionInput}
                            value={description}
                            onChange={(e) =>
                              setDescription(e.target.value.slice(0, 500))
                            }
                            placeholder="可选：描述您希望对图片进行的编辑操作，例如：调整亮度、修改颜色、添加特效、风格转换等..."
                            rows={3}
                            maxLength={500}
                          />
                        </div>
                      )}

                      {/* 处理按钮 */}
                      <button
                        className={`${styles.processButton} ${
                          canProcess ? styles.enabled : styles.disabled
                        }`}
                        onClick={handleProcess}
                        disabled={!canProcess}
                      >
                        {isProcessing
                          ? "处理中..."
                          : editMode === "inpaint"
                          ? "开始重绘"
                          : "开始消除"}
                      </button>

                      {/* 底部信息 */}
                      <div className={styles.bottomInfo}>
                        <div className={styles.credits}>
                          <span className={styles.creditsCost}>
                            消耗50创想值
                          </span>
                          <span className={styles.creditsDivider}>•</span>
                          <span
                            className={styles.creditsLink}
                            onClick={() => navigate(Path.Recharge)}
                          >
                            获取更多创想值
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === "result" && (
                    <div className={styles.resultTab}>
                      {/* 处理中状态 */}
                      {isProcessing && !taskId && (
                        <div className={styles.statusContainer}>
                          <div className={styles.processingCard}>
                            <div className={styles.statusRow}>
                              <div className={styles.statusLeft}>
                                <div className={styles.spinner}></div>
                                <span className={styles.statusLabel}>
                                  处理中
                                </span>
                              </div>
                              <span className={styles.timeLabel}>
                                {new Date().toLocaleTimeString("zh-CN", {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                })}
                              </span>
                            </div>
                            {editMode === "inpaint" && description.trim() && (
                              <div className={styles.promptText}>
                                &ldquo;{description}&rdquo;
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* 任务已提交状态 */}
                      {taskId && (
                        <div className={styles.statusContainer}>
                          <div className={styles.successCard}>
                            <div className={styles.successRow}>
                              <div className={styles.checkIcon}>✓</div>
                              <div className={styles.successText}>
                                <div className={styles.successTitle}>
                                  任务已提交
                                </div>
                                <div className={styles.successDesc}>
                                  可前往“我的作品”页面查看结果
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 空状态 */}
                      {!isProcessing && !taskStatus && (
                        <div className={styles.emptyResult}>
                          <div className={styles.emptyIcon}>🎨</div>
                          <h3>暂无任务</h3>
                          <p>选择编辑模式并点击按钮开始处理图片</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 局部重绘弹框 */}
      {showInpaintModal && uploadedImage && (
        <div className={styles.inpaintModal}>
          <div className={styles.inpaintModalContent}>
            {/* 关闭按钮 */}
            <div className={styles.inpaintHeader}>
              <button
                className={styles.inpaintCloseButton}
                onClick={handleCloseInpaint}
              >
                <CloseIcon />
              </button>
            </div>

            {/* 图片显示区域 */}
            <div className={styles.inpaintBody}>
              <div className={styles.inpaintCanvas}>
                <div className={styles.canvasContainer}>
                  <img
                    key={uploadedImage} // 强制重新渲染
                    ref={imageRef}
                    src={
                      editMode === "inpaint"
                        ? inpaintPreviewImage || uploadedImage!
                        : removePreviewImage || uploadedImage!
                    }
                    alt="编辑图片"
                    crossOrigin="anonymous"
                    onLoad={initializeCanvas}
                  />
                  <canvas
                    ref={canvasRef}
                    onMouseDown={startDrawing}
                    onMouseMove={draw}
                    onMouseUp={stopDrawing}
                    onMouseLeave={stopDrawing}
                    onTouchStart={(e) => {
                      e.preventDefault();
                      const touch = e.touches[0];
                      const mouseEvent = new MouseEvent("mousedown", {
                        clientX: touch.clientX,
                        clientY: touch.clientY,
                      });
                      startDrawing(mouseEvent as any);
                    }}
                    onTouchMove={(e) => {
                      e.preventDefault();
                      const touch = e.touches[0];
                      const mouseEvent = new MouseEvent("mousemove", {
                        clientX: touch.clientX,
                        clientY: touch.clientY,
                      });
                      draw(mouseEvent as any);
                    }}
                    onTouchEnd={(e) => {
                      e.preventDefault();
                      stopDrawing();
                    }}
                  />
                </div>
              </div>

              {/* 底部工具栏 */}
              <div className={styles.inpaintToolbar}>
                <div className={styles.toolbarLeft}>
                  {/* 画笔工具切换 */}
                  <div className={styles.toolButtons}>
                    <button
                      className={`${styles.toolButton} ${
                        currentTool === "brush" ? styles.active : ""
                      }`}
                      onClick={() => setCurrentTool("brush")}
                      title="画笔"
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M12 19l7-7 3 3-7 7-3-3z" />
                        <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z" />
                        <path d="M2 2l7.586 7.586" />
                        <circle cx="11" cy="11" r="2" />
                      </svg>
                    </button>
                    <button
                      className={`${styles.toolButton} ${
                        currentTool === "eraser" ? styles.active : ""
                      }`}
                      onClick={() => setCurrentTool("eraser")}
                      title="橡皮擦"
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M7 21h10" />
                        <path d="M5 21h1.5a2 2 0 0 0 1.4-.6l7.5-7.5a2 2 0 0 0 0-2.8L13.8 8.5a2 2 0 0 0-2.8 0l-7.5 7.5a2 2 0 0 0-.6 1.4V19a2 2 0 0 0 2 2Z" />
                      </svg>
                    </button>
                  </div>

                  {/* 画笔大小控制 */}
                  <div className={styles.brushSizeControl}>
                    <input
                      type="range"
                      min="5"
                      max="100"
                      value={brushSize}
                      onChange={(e) => setBrushSize(Number(e.target.value))}
                      className={styles.sizeSlider}
                    />
                    <div className={styles.sizeDisplay}>{brushSize}</div>
                  </div>
                </div>

                <div className={styles.toolbarRight}>
                  {/* 操作按钮 */}
                  <div className={styles.toolButtons}>
                    {/* 上一步 */}
                    <button
                      className={styles.toolButton}
                      onClick={handleUndo}
                      disabled={historyIndex <= 0}
                      title="上一步"
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M3 7v6h6" />
                        <path d="M21 17a9 9 0 00-9-9 9 9 0 00-6 2.3L3 13" />
                      </svg>
                    </button>

                    {/* 下一步 */}
                    <button
                      className={styles.toolButton}
                      onClick={handleRedo}
                      disabled={historyIndex >= maskHistory.length - 1}
                      title="下一步"
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M21 7v6h-6" />
                        <path d="M3 17a9 9 0 019-9 9 9 0 016 2.3L21 13" />
                      </svg>
                    </button>

                    {/* 重置 */}
                    <button
                      className={styles.toolButton}
                      onClick={handleReset}
                      title="重置"
                    >
                      <svg
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M1 4v6h6" />
                        <path d="M3.51 15a9 9 0 102.13-9.36L1 10" />
                      </svg>
                    </button>
                  </div>

                  {/* 确认按钮 */}
                  <button
                    className={styles.confirmButton}
                    onClick={handleConfirmInpaint}
                  >
                    确认
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </React.Fragment>
  );
}
