import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getUserInfo, UserProfile } from "../api/custom-api/auth-api";
import { StripeApi, UserBalance } from "../api/custom-api/stripe-api";
import { isAuthenticated } from "../utils/auth-token";
import { Path } from "../constant";
import styles from "./my-account.module.scss";
import Locale from "../locales";
import { IconButton } from "./button";
import CloseIcon from "../icons/close.svg";
import RechargeIcon from "../icons/add.svg";
import { showToast } from "./ui-lib";

// 我的账户页面组件
export function MyAccountPage() {
  const navigate = useNavigate();

  // 检查用户是否已登录
  useEffect(() => {
    if (!isAuthenticated()) {
      navigate(Path.Login);
      return;
    }
  }, [navigate]);

  const [userInfo, setUserInfo] = useState<UserProfile | null>(null);
  const [userBalance, setUserBalance] = useState<UserBalance | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载用户余额
  const loadUserBalance = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data: any = await StripeApi.getUserBalance();
      const result = data?.rows[0] || {};
      const balance = {
        coins: result.coinBalance || 0,
      };
      setUserBalance(balance);
    } catch (error: any) {
      console.error("Load balance error:", error);
      setError("获取余额失败");
      // 设置默认值
      setUserBalance({ coins: 0 });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // 动态获取用户信息
    const currentUserInfo = getUserInfo();
    setUserInfo(currentUserInfo);

    // 如果用户未登录，跳转到登录页面
    if (!currentUserInfo) {
      navigate(Path.Login);
      return;
    }

    loadUserBalance();

    // 定期检查用户信息变化（用于处理账户切换）
    const checkUserInfo = () => {
      const latestUserInfo = getUserInfo();
      if (latestUserInfo?.account !== currentUserInfo?.account) {
        setUserInfo(latestUserInfo);
        if (latestUserInfo) {
          loadUserBalance(); // 重新加载新用户的余额
        }
      }
    };

    const interval = setInterval(checkUserInfo, 1000); // 每秒检查一次

    return () => {
      clearInterval(interval);
    };
  }, [navigate]);

  // 跳转到充值页面
  const handleRecharge = () => {
    navigate(Path.Recharge);
  };

  // 跳转到充值历史页面
  const handleRechargeHistory = () => {
    navigate(Path.RechargeHistory);
  };

  // 刷新余额
  const handleRefreshBalance = () => {
    loadUserBalance();
    showToast("余额已刷新");
  };

  // 格式化余额显示
  const formatBalance = (coins: number) => {
    return new Intl.NumberFormat("zh-CN").format(coins);
  };

  // 格式化时间显示
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString("zh-CN");
  };

  if (!userInfo) {
    return null;
  }

  return (
    <div className={styles["my-account-page"]}>
      {/* 头部 */}
      <div className={styles["header"]}>
        <div className={styles["header-left"]}>
          <h1 className={styles["title"]}>我的账户</h1>
        </div>
        <button
          className={styles["close-button"]}
          onClick={() => navigate(Path.Home)}
        >
          <CloseIcon />
        </button>
      </div>

      <div className={styles["content"]}>
        {/* 侧边栏 */}
        <div className={styles["sidebar"]}>
          {/* 用户信息卡片 */}
          <div className={styles["user-info-card"]}>
            <div className={styles["avatar"]}>
              {userInfo.account.charAt(0).toUpperCase()}
            </div>
            <div className={styles["user-details"]}>
              <h3 className={styles["username"]}>{userInfo.account}</h3>
              <p className={styles["org-code"]}>{userInfo.orgCode}</p>
            </div>
          </div>

          {/* 余额卡片 */}
          <div className={styles["balance-card"]}>
            <div className={styles["balance-header"]}>
              <h3>创想值余额</h3>
              <button
                className={styles["refresh-button"]}
                onClick={handleRefreshBalance}
                disabled={isLoading}
              >
                {isLoading ? "刷新中..." : "刷新"}
              </button>
            </div>

            {error ? (
              <div className={styles["error-message"]}>
                <p>{error}</p>
                <button onClick={loadUserBalance}>重试</button>
              </div>
            ) : (
              <div className={styles["balance-content"]}>
                <div className={styles["balance-amount"]}>
                  <span className={styles["amount"]}>
                    {userBalance ? formatBalance(userBalance.coins) : "--"}
                  </span>
                  <span className={styles["unit"]}>创想值</span>
                </div>
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className={styles["action-buttons"]}>
            <button
              className={styles["recharge-button"]}
              onClick={handleRecharge}
            >
              <RechargeIcon className={styles["button-icon"]} />
              充值
            </button>

            <button
              className={styles["history-button"]}
              onClick={handleRechargeHistory}
            >
              充值记录
            </button>
          </div>
        </div>

        {/* 主内容区域 */}
        <div className={styles["main-content"]}>
          <div className={styles["content-card"]}>
            <h2>账户概览</h2>

            <div className={styles["overview-grid"]}>
              <div className={styles["overview-item"]}>
                <h4>账户状态</h4>
                <p className={styles["overview-value"]}>正常</p>
              </div>

              <div className={styles["overview-item"]}>
                <h4>最后登录</h4>
                <p className={styles["overview-value"]}>{userInfo.loginTime}</p>
              </div>
            </div>
          </div>

          <div className={styles["content-card"]}>
            <h2>快速操作</h2>

            <div className={styles["quick-actions"]}>
              <div className={styles["action-item"]} onClick={handleRecharge}>
                <div className={styles["action-icon"]}>
                  <RechargeIcon />
                </div>
                <div className={styles["action-text"]}>
                  <h4>充值创想值</h4>
                  <p>购买更多创想值以继续使用服务</p>
                </div>
              </div>

              <div
                className={styles["action-item"]}
                onClick={handleRechargeHistory}
              >
                <div className={styles["action-icon"]}>📊</div>
                <div className={styles["action-text"]}>
                  <h4>查看充值记录</h4>
                  <p>查看历史充值和消费记录</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
