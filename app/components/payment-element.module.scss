@import "../styles/animation.scss";

.payment-form {
  max-width: 100%;
  margin: 0;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 0;
  box-shadow: none;
  border: none;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  
  @media (prefers-color-scheme: dark) {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-color: rgba(71, 85, 105, 0.6);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 8px 16px rgba(0, 0, 0, 0.2);

    &::before {
      background: linear-gradient(90deg, #93c5fd 0%, #c084fc 50%, #fbbf24 100%);
    }
  }
  

}

.payment-header {
  text-align: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
  flex-shrink: 0; /* 防止头部被压缩 */
  padding-bottom: 1rem;

  @media (prefers-color-scheme: dark) {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .payment-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.5rem;
    letter-spacing: 0;
    line-height: 1.3;

    @media (prefers-color-scheme: dark) {
      color: #f9fafb;
    }

    @media (max-width: 640px) {
      font-size: 1.125rem;
    }

    @media (max-width: 480px) {
      font-size: 1rem;
    }
  }

.payment-content {
  flex: 1;
  overflow: visible; /* 移除内部滚动，让外层弹框处理 */
  padding-bottom: 1rem;
  min-height: 0; /* 确保flex子元素能够正确收缩 */
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 2px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    
    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
  }
}

.package-summary {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2.5rem;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
      pointer-events: none;
    }

    @media (prefers-color-scheme: dark) {
      background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
      border-color: rgba(75, 85, 99, 0.8);
    }

    @media (max-width: 640px) {
      padding: 1.5rem;
      border-radius: 12px;
    }

    .package-name {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary);
      margin-bottom: 8px;
    }

    .package-details {
      font-size: 15px;
      color: var(--black-60);
      line-height: 1.5;
    }

    .package-coins {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.package-price {
  font-size: 18px;
  font-weight: 700;
  color: #007bff;
  text-align: center;
  padding: 8px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #dee2e6;
}
  }
}

.payment-element-container {
  margin-bottom: 28px;
  
  // Stripe Elements 样式覆盖
  :global(.StripeElement) {
    padding: 20px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.05);
      transform: translateY(-1px);
    }
  }

  :global(.StripeElement--invalid) {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }

  :global(.StripeElement--complete) {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }
}

// Express Checkout Element 容器样式
.express-checkout-container {
  margin-bottom: 24px;
  padding: 6px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  
  // Express Checkout Element 样式覆盖
  :global(.StripeExpressCheckoutElement) {
    border-radius: 12px;
    overflow: hidden;
    min-height: 52px;
  }
  
  // Google Pay 按钮样式优化（优先显示）
  :global(.StripeExpressCheckoutElement button) {
    border-radius: 12px !important;
    height: 52px !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 8px !important;
    width: 100% !important;
    
    &:hover {
      transform: translateY(-2px) scale(1.01) !important;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
    }
    
    &:active {
      transform: translateY(0) scale(0.98) !important;
    }
    
    // Google Pay 按钮特殊样式
    &[data-testid*="google"], &[aria-label*="Google"] {
      background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 75%, #ea4335 100%) !important;
      color: white !important;
      border: none !important;
      font-weight: 700 !important;
      order: -1 !important; // 确保显示在最前面
    }
  }
}

// 支付方式分隔线
.payment-divider {
  display: flex;
  align-items: center;
  margin: 28px 0;
  
  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
  }
  
  span {
    padding: 0 20px;
    font-size: 14px;
    font-weight: 500;
    color: var(--black-60);
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: 20px;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }
}

.error-message {
  color: var(--red);
  font-size: 14px;
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(223, 27, 65, 0.1);
  border: 1px solid rgba(223, 27, 65, 0.2);
  border-radius: 8px;
  text-align: center;
}

.payment-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
  margin-bottom: 0;
  flex-shrink: 0;
  padding: 1rem 0 0;
  border-top: 1px solid #e5e7eb;
  background: #ffffff;

  .cancel-button {
    flex: 1;
    padding: 0.75rem 1.5rem;
    background: #f9fafb;
    color: #374151;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: #f3f4f6;
      border-color: #9ca3af;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .pay-button {
    flex: 2;
    padding: 0.75rem 1.5rem;
    background: #4f46e5;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    &:hover:not(:disabled) {
      background: #4338ca;
    }

    &:active:not(:disabled) {
      background: #3730a3;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background: #9ca3af;
    }

    .loading-spinner {
      width: 18px;
      height: 18px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

.security-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 13px;
  color: var(--black-60);
  text-align: center;
  margin-top: 16px;

  .security-icon {
    width: 16px;
    height: 16px;
    color: var(--green);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-in-light);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .loading-text {
    font-size: 16px;
    color: var(--black-60);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  .error-text {
    font-size: 16px;
    color: var(--red);
    margin-bottom: 20px;
  }

  .retry-button {
    padding: 12px 24px;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--primary-dark);
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .payment-form {
    margin: 0; /* 移除外边距 */
    padding: 1.5rem;
    max-height: none; /* 移除高度限制 */
    border-radius: 0; /* 移除圆角 */
    max-width: none; /* 移除宽度限制 */
  }

  .payment-header {
    margin-bottom: 1rem;

    .payment-title {
      font-size: 1.375rem;
    }
  }

  .address-summary {
    margin-bottom: 1rem;
  }

  .payment-actions {
    margin-bottom: 0; /* 移除底部边距 */
    padding: 1rem 0 0.5rem; /* 调整内边距 */
  }
}

@media (max-width: 480px) {
  .payment-form {
    margin: 0; /* 移除外边距 */
    padding: 1rem;
    max-height: none; /* 移除高度限制 */
    border-radius: 0; /* 移除圆角 */
    max-width: none; /* 移除宽度限制 */
  }

  .payment-header {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;

    .payment-title {
      font-size: 1.25rem;
      margin-bottom: 0.75rem;
    }
    
    .package-summary {
      .package-name {
        font-size: 16px;
      }
      
      .package-details {
        font-size: 14px;
      }
      
      .package-price {
        font-size: 16px;
        padding: 6px 12px;
      }
    }
  }

  .express-checkout-container {
    margin-bottom: 20px;
    padding: 4px;
    
    :global(.StripeExpressCheckoutElement button) {
      height: 48px !important;
      font-size: 15px !important;
    }
  }

  .payment-element-container {
    margin-bottom: 20px;
    
    :global(.StripeElement) {
      padding: 16px;
    }
  }

  .payment-actions {
    flex-direction: column;
    gap: 10px;
    margin-bottom: 0;
    padding: 0.75rem 0 0.5rem;

    .cancel-button,
    .pay-button {
      flex: none;
      padding: 14px 20px; /* 增加按钮高度，便于点击 */
      font-size: 16px; /* 增加字体大小 */
      min-height: 48px; /* 确保最小点击区域 */
    }

    .pay-button {
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .security-info {
    font-size: 12px;
  }
}

@media (max-width: 360px) {
  .payment-form {
    margin: 0;
    padding: 0.75rem;
    max-height: none;
  }

  .payment-header {
    margin-bottom: 0.75rem;

    .payment-title {
      font-size: 1.125rem;
    }
  }

  .payment-actions {
    padding: 0.5rem 0 0.25rem;

    .cancel-button,
    .pay-button {
      padding: 12px 16px;
      min-height: 44px;
      font-size: 15px;
    }
  }

  .express-checkout-container {
    :global(.StripeExpressCheckoutElement button) {
      height: 44px !important;
      font-size: 14px !important;
    }
  }

  .address-container {
    padding: 0.75rem;
  }

  .order-summary {
    margin-bottom: 1rem;
  }

  .address-actions {
    gap: 12px;
    padding-top: 1rem;
    margin-top: auto;

    .continue-button,
    .cancel-button {
      padding: 12px 16px;
      font-size: 0.9375rem;
      min-height: 44px;
    }
  }
}

// 地址收集界面样式
.address-container {
  max-width: 100%;
  margin: 0;
  padding: 0;
  background: #ffffff;
  border-radius: 0;
  box-shadow: none;
  border: none;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

// 表单分组样式
.form-section {
  margin-bottom: 1.5rem;

  &:last-child {
    margin-bottom: 1rem;
  }
}

.section-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #4f46e5;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e0e7ff;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  @media (prefers-color-scheme: dark) {
    color: #a5b4fc;
    border-bottom-color: rgba(165, 180, 252, 0.2);
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;

  @media (max-width: 640px) {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

/* 订单摘要样式 */
.address-summary {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  position: relative;
  flex-shrink: 0;

  @media (prefers-color-scheme: dark) {
    background: #1f2937;
    border-color: #374151;
  }

  @media (max-width: 640px) {
    padding: 0.75rem;
    border-radius: 6px;
  }
}

.address-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.75rem 0;
  letter-spacing: 0;

  @media (prefers-color-scheme: dark) {
    color: #f3f4f6;
  }

  @media (max-width: 640px) {
    font-size: 0.8rem;
  }
}

.address-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
  z-index: 1;
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.875rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  @media (prefers-color-scheme: dark) {
    background: rgba(30, 41, 59, 0.7);
    border-color: rgba(71, 85, 105, 0.5);

    &:hover {
      background: rgba(30, 41, 59, 0.9);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
  }
}

.address-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  letter-spacing: -0.025em;

  @media (prefers-color-scheme: dark) {
    color: #94a3b8;
  }

  @media (max-width: 640px) {
    font-size: 0.8125rem;
  }
}

.address-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: -0.025em;

  @media (prefers-color-scheme: dark) {
    color: #e2e8f0;
  }

  @media (max-width: 640px) {
    font-size: 0.8125rem;
  }
}

.order-summary {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 2rem;
  flex-shrink: 0;

  @media (prefers-color-scheme: dark) {
    background: #1f2937;
    border-bottom-color: #374151;
  }

  @media (max-width: 768px) {
    padding: 1.5rem;
  }

  @media (max-width: 480px) {
    padding: 1.25rem;
  }
}

.order-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
  text-align: center;

  @media (prefers-color-scheme: dark) {
    color: #f9fafb;
  }
}

.package-info {
  text-align: center;
  background: #f8fafc;
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid #e2e8f0;

  @media (prefers-color-scheme: dark) {
    background: #374151;
    border-color: #4b5563;
  }
}

.package-name {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;

  @media (prefers-color-scheme: dark) {
    color: #f3f4f6;
  }
}

.package-details {
  font-size: 1.125rem;
  font-weight: 700;
  color: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &::before {
    content: '💎';
    font-size: 1.25rem;
  }

  @media (prefers-color-scheme: dark) {
    color: #a5b4fc;
  }
}

// 滚动内容区域
.address-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 2rem;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }

  @media (max-width: 480px) {
    padding: 1.25rem;
  }
}

.address-actions {
  display: flex;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e7eb;
  background: #ffffff;
  flex-shrink: 0;

  @media (prefers-color-scheme: dark) {
    background: #1f2937;
    border-top-color: #374151;
  }

  @media (max-width: 768px) {
    padding: 1.25rem 1.5rem;
  }

  @media (max-width: 480px) {
    padding: 1rem;
    flex-direction: column;
    gap: 0.75rem;
  }
}

.cancel-button {
  flex: 1;
  padding: 0.75rem 1.5rem;
  background: #f9fafb;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: #f3f4f6;
    border-color: #9ca3af;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  @media (prefers-color-scheme: dark) {
    background: #374151;
    color: #f3f4f6;
    border-color: #4b5563;

    &:hover:not(:disabled) {
      background: #4b5563;
      border-color: #6b7280;
    }
  }
}

.continue-button {
  flex: 2;
  padding: 0.75rem 1.5rem;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:hover:not(:disabled) {
    background: #4338ca;
  }

  &:active:not(:disabled) {
    background: #3730a3;
  }

  &:disabled,
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #9ca3af;
  }

  @media (prefers-color-scheme: dark) {
    background: #6366f1;

    &:hover:not(:disabled) {
      background: #5b21b6;
    }

    &:active:not(:disabled) {
      background: #4c1d95;
    }
  }
}