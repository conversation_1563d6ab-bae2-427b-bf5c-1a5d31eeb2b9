"use client";

import React, { useState, useEffect } from "react";
import {
  Elements,
  PaymentElement,
  ExpressCheckoutElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import styles from "./payment-element.module.scss";
import { RechargePackage, StripeApi } from "../api/custom-api";
// 移除地址收集组件的导入，不再需要

// Stripe 公钥 - 从环境变量获取
const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
);

interface PaymentFormProps {
  clientSecret: string;
  selectedPackage: RechargePackage;
  // 移除 addressInfo，不再需要
  onSuccess: () => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

// 支付表单组件
function PaymentForm({
  clientSecret,
  selectedPackage,
  // 移除 addressInfo 参数
  onSuccess,
  onError,
  onCancel,
}: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setMessage(null);

    // 在提交前验证表单
    const { error: submitError } = await elements.submit();
    if (submitError) {
      setMessage(submitError.message || "请检查您的支付信息");
      onError(submitError.message || "请检查您的支付信息");
      setIsLoading(false);
      return;
    }

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/#/recharge?success=true`,
          // 移除 payment_method_data，让 Stripe Elements 自己处理所有信息
        },
        redirect: "if_required", // 避免不必要的重定向
      });

      if (error) {
        let errorMessage = "支付失败";

        if (error.type === "card_error") {
          // 处理银行卡相关错误
          switch (error.code) {
            case "incomplete_number":
              errorMessage = "银行卡号不完整，请检查卡号";
              break;
            case "incomplete_cvc":
              errorMessage = "安全码不完整，请检查CVC";
              break;
            case "incomplete_expiry":
              errorMessage = "有效期不完整，请检查月份和年份";
              break;
            case "invalid_number":
              errorMessage = "银行卡号无效，请检查卡号";
              break;
            case "invalid_expiry_month":
              errorMessage = "有效期月份无效";
              break;
            case "invalid_expiry_year":
              errorMessage = "有效期年份无效";
              break;
            case "invalid_cvc":
              errorMessage = "安全码无效";
              break;
            case "card_declined":
              // 根据具体的拒绝原因提供更详细的错误信息
              if (error.decline_code === "fraudulent") {
                errorMessage =
                  "支付被安全系统拒绝。建议：\n1. 使用真实的个人信息\n2. 确保邮箱地址有效\n3. 尝试使用借记卡或信用卡（非预付卡）\n4. 联系银行确认卡片状态";
              } else if (error.decline_code === "generic_decline") {
                errorMessage =
                  "银行拒绝了此次交易，请联系您的银行或尝试其他支付方式";
              } else if (error.decline_code === "insufficient_funds") {
                errorMessage = "账户余额不足，请检查余额或使用其他卡片";
              } else if (
                error.decline_code === "lost_card" ||
                error.decline_code === "stolen_card"
              ) {
                errorMessage = "卡片已被标记为丢失或被盗，请联系银行";
              } else if (error.decline_code === "pickup_card") {
                errorMessage = "请联系银行处理卡片问题";
              } else if (error.decline_code === "restricted_card") {
                errorMessage = "卡片使用受限，请联系银行或使用其他卡片";
              } else if (error.decline_code === "security_violation") {
                errorMessage = "安全验证失败，请确保输入正确的卡片信息";
              } else if (error.decline_code === "service_not_allowed") {
                errorMessage = "此卡片不支持在线支付，请使用其他卡片";
              } else if (error.decline_code === "transaction_not_allowed") {
                errorMessage = "此类交易不被允许，请联系银行或使用其他支付方式";
              } else {
                errorMessage = `银行卡被拒绝（${
                  error.decline_code || "未知原因"
                }），请联系您的银行或使用其他卡片`;
              }
              break;
            case "insufficient_funds":
              errorMessage = "余额不足，请检查账户余额";
              break;
            default:
              errorMessage = error.message || "银行卡验证失败";
          }
        } else if (error.type === "validation_error") {
          errorMessage = error.message || "请检查您的支付信息";
        } else {
          errorMessage = "支付过程中发生意外错误";
        }

        setMessage(errorMessage);
        onError(errorMessage);
      } else if (paymentIntent && paymentIntent.status === "succeeded") {
        // 支付成功
        onSuccess();
      }
    } catch (err: any) {
      setMessage(err.message || "支付失败");
      onError(err.message || "支付失败");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={styles["payment-form"]}>
      <div className={styles["payment-header"]}>
        <h3 className={styles["payment-title"]}>完成支付</h3>
        <div className={styles["package-summary"]}>
          <div className={styles["package-name"]}>{selectedPackage.title}</div>
          <div className={styles["package-details"]}>
            <div className={styles["package-coins"]}>
              {selectedPackage.coins} 创想值
            </div>
            <div className={styles["package-price"]}>
              ${selectedPackage.price}
            </div>
          </div>
        </div>
      </div>

      <div className={styles["payment-content"]}>
        {/* 支付提示信息 */}
        <div className={styles["payment-info"]}>
          <p>💡 支付表单由 Stripe 安全提供，请按提示填写所需信息</p>
          <p>系统会自动显示适合您的支付方式和必填字段</p>
        </div>

        <div className={styles["payment-element-container"]}>
          {/* Express Checkout Element - 用于显示一键支付按钮（Google Pay, Apple Pay等） */}
          <div className={styles["express-checkout-container"]}>
            <ExpressCheckoutElement
              // 使用 Stripe 默认配置，自动显示可用的支付方式
              onConfirm={async () => {
                const { error: submitError } = await elements!.submit();
                if (submitError) {
                  setMessage(submitError.message || "请检查您的支付信息");
                  onError(submitError.message || "请检查您的支付信息");
                  return;
                }

                const { error } = await stripe!.confirmPayment({
                  elements: elements!,
                  confirmParams: {
                    return_url: `${window.location.origin}/#/recharge?success=true`,
                    // 移除 payment_method_data，让 Stripe Elements 自己处理所有信息
                  },
                  redirect: "if_required",
                });

                if (error) {
                  setMessage(error.message || "支付失败");
                  onError(error.message || "支付失败");
                } else {
                  onSuccess();
                }
              }}
              onCancel={() => console.log("Express checkout cancelled")}
            />
          </div>

          {/* 分隔线 */}
          <div className={styles["payment-divider"]}>
            <span>或</span>
          </div>

          {/* 传统支付表单 */}
          <PaymentElement
            // 完全移除 options 配置，使用 Stripe 默认行为
          />
        </div>

        {message && <div className={styles["error-message"]}>{message}</div>}
      </div>

      <div className={styles["payment-actions"]}>
        <button
          type="button"
          className={styles["cancel-button"]}
          onClick={onCancel}
          disabled={isLoading}
        >
          取消
        </button>
        <button
          type="submit"
          className={styles["pay-button"]}
          disabled={!stripe || isLoading}
        >
          {isLoading ? (
            <>
              <span className={styles["loading-spinner"]}></span>
              处理中...
            </>
          ) : (
            `支付 $${selectedPackage.price}`
          )}
        </button>
      </div>

      <div className={styles["security-info"]}>
        <svg
          className={styles["security-icon"]}
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H16V16H8V11H9.2V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.4,8.7 10.4,10V11H13.6V10C13.6,8.7 12.8,8.2 12,8.2Z" />
        </svg>
        <span>安全支付由 Stripe 提供</span>
      </div>
    </form>
  );
}

interface PaymentElementWrapperProps {
  selectedPackage: RechargePackage;
  onSuccess: () => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

// 主支付组件包装器
export function PaymentElementWrapper({
  selectedPackage,
  onSuccess,
  onError,
  onCancel,
}: PaymentElementWrapperProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  // 移除不需要的状态

  // 初始化组件，直接创建 PaymentIntent
  useEffect(() => {
    createPaymentIntent();
  }, []);

  // 创建 Payment Intent - 简化版，不传递地址信息
  const createPaymentIntent = async () => {
    try {
      setIsLoading(true);
      const data = await StripeApi.createPaymentIntent({
        productName: selectedPackage.title,
        amount: selectedPackage.price * 100, // 转换为分
        quantity: 1,
        coinAmount: selectedPackage.coins,
      });

      setClientSecret(data.clientSecret);
      // 移除 setShowPayment，直接显示支付表单
    } catch (error: any) {
      onError(error.message || "创建支付失败");
    } finally {
      setIsLoading(false);
    }
  };

  // 移除不需要的函数

  if (isLoading) {
    return (
      <div className={styles["loading-container"]}>
        <div className={styles["loading-spinner"]}></div>
        <div className={styles["loading-text"]}>正在初始化...</div>
      </div>
    );
  }

  // 移除地址收集界面，直接显示支付表单

  if (!clientSecret) {
    return (
      <div className={styles["error-container"]}>
        <div className={styles["error-text"]}>支付初始化失败</div>
        <button
          className={styles["retry-button"]}
          onClick={() => createPaymentIntent()}
        >
          重试
        </button>
      </div>
    );
  }

  const options = {
    clientSecret,
    // 移除所有 appearance 配置，使用 Stripe 默认外观
  };

  return (
    <Elements options={options} stripe={stripePromise}>
      <PaymentForm
        clientSecret={clientSecret}
        selectedPackage={selectedPackage}
        onSuccess={onSuccess}
        onError={onError}
        onCancel={onCancel}
      />
    </Elements>
  );
}
