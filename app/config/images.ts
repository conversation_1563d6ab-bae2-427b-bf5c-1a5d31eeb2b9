// 图片资源配置文件

export interface ExampleImage {
  id: number;
  beforeImage: string;
  afterImage: string;
  description?: string;
}

export interface MaterialImage {
  id: number;
  path: string;
}

// 图片重绘案例配置
export const IMAGE_REDRAW_EXAMPLES: ExampleImage[] = [
  {
    id: 1,
    beforeImage: "/images/examples/image-redraw-1-before.jpg",
    afterImage: "/images/examples/image-redraw-1-after.jpg",
  },
];

// 万物删除案例配置
export const IMAGE_REMOVE_EXAMPLES: ExampleImage[] = [
  {
    id: 1,
    beforeImage: "/images/examples/image-remove-1-before.jpg",
    afterImage: "/images/examples/image-remove-1-after.jpg",
  },
];

// 万物替换案例配置
export const OBJECT_REPLACE_EXAMPLES: ExampleImage[] = [
  {
    id: 1,
    beforeImage: "/images/examples/object-replace-1-before.jpg",
    afterImage: "/images/examples/object-replace-1-after.jpg",
  },
];

// 证件照案例配置
export const ID_PHOTO_EXAMPLES: ExampleImage[] = [
  {
    id: 1,
    beforeImage: "/images/examples/id-photo-generate-1-before.avif",
    afterImage: "/images/examples/id-photo-generate-1-after.avif",
  },
];

// 海报生成案例配置
export const POSTER_GENERATE_EXAMPLES: ExampleImage[] = [
  {
    id: 1,
    beforeImage: "/images/examples/poster-generate-1.avif",
    afterImage: "/images/examples/poster-generate-2.avif",
  },
];

// 素材图片配置
export const MATERIAL_IMAGES: MaterialImage[] = [
  {
    id: 1,
    path: "/images/materials/common-1.png",
  },
  {
    id: 2,
    path: "/images/materials/common-2.png",
  },
  {
    id: 3,
    path: "/images/materials/common-3.jpeg",
  },
  {
    id: 4,
    path: "/images/materials/common-4.png",
  },
];

// 工具函数
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

export const preloadImages = async (srcs: string[]): Promise<void> => {
  try {
    await Promise.all(srcs.map(preloadImage));
  } catch (error) {
    console.warn("Some images failed to preload:", error);
  }
};

// 获取案例图片路径（用于预加载）
export const getImageRedrawExamplePaths = (): string[] => {
  return IMAGE_REDRAW_EXAMPLES.flatMap((example) => [
    example.beforeImage,
    example.afterImage,
  ]);
};

export const getImageRemoveExamplePaths = (): string[] => {
  return IMAGE_REMOVE_EXAMPLES.flatMap((example) => [
    example.beforeImage,
    example.afterImage,
  ]);
};

export const getObjectReplaceExamplePaths = (): string[] => {
  return OBJECT_REPLACE_EXAMPLES.flatMap((example) => [
    example.beforeImage,
    example.afterImage,
  ]);
};

export const getIdPhotoExamplePaths = (): string[] => {
  return ID_PHOTO_EXAMPLES.flatMap((example) => [
    example.beforeImage,
    example.afterImage,
  ]);
};
