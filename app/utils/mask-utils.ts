/**
 * 遮罩工具类 - 参考iOS CustomSmudgeImageView实现
 * 确保所有功能页面使用完全一致的遮罩生成逻辑
 */

// 空PNG文件的字节数组（用作fallback）
const EMPTY_PNG_BYTES = new Uint8Array([
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49,
  0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06,
  0x00, 0x00, 0x00, 0x1f, 0x15, 0xc4, 0x89, 0x00, 0x00, 0x00, 0x0a, 0x49, 0x44,
  0x41, 0x54, 0x78, 0x9c, 0x63, 0x00, 0x01, 0x00, 0x00, 0x05, 0x00, 0x01, 0x0d,
  0x0a, 0x2d, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42,
  0x60, 0x82,
]);

/**
 * 将base64转换为File对象
 */
export function base64ToFile(base64: string, filename: string): File {
  try {
    // 验证base64字符串格式
    if (!base64 || typeof base64 !== "string") {
      throw new Error("Invalid base64 string: empty or not a string");
    }

    // 检查是否是data URL格式
    if (!base64.startsWith("data:")) {
      throw new Error("Invalid base64 string: not a data URL");
    }

    // 检查是否包含逗号
    if (!base64.includes(",")) {
      throw new Error("Invalid base64 string: no comma separator");
    }

    const arr = base64.split(",");
    if (arr.length !== 2) {
      console.error("Split result:", arr);
      throw new Error(
        `Invalid base64 string: incorrect format, got ${arr.length} parts`,
      );
    }

    // 检查第一部分是否为空
    if (!arr[0] || arr[0].trim() === "") {
      throw new Error("Invalid base64 string: empty header part");
    }

    const mimeMatch = arr[0].match(/:(.*?);/);
    const mime = mimeMatch?.[1] || "image/png";
    const base64Data = arr[1];

    // 验证base64数据部分
    if (!base64Data || base64Data.trim() === "") {
      throw new Error("Invalid base64 string: no data part");
    }

    // 验证base64格式 - 放宽一些，允许换行符
    const cleanBase64Data = base64Data.replace(/\s/g, "");
    const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
    if (!cleanBase64Data || !base64Regex.test(cleanBase64Data)) {
      throw new Error("Invalid base64 string: invalid characters");
    }

    const bstr = atob(cleanBase64Data);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  } catch (error) {
    console.error("base64ToFile error:", error);
    console.error("base64 string length:", base64?.length);
    console.error("base64 string start:", base64?.substring(0, 50));
    console.error("base64 string contains comma:", base64?.includes(","));

    // 返回一个空的PNG文件作为fallback，避免抛出错误
    return new File([EMPTY_PNG_BYTES], filename, { type: "image/png" });
  }
}

/**
 * 创建黑白遮罩图片（参考iOS实现）
 * 黑色背景，白色涂抹区域
 */
export function createBlackWhiteMask(
  canvas: HTMLCanvasElement,
): HTMLCanvasElement {
  // 创建黑白遮罩画布
  const maskCanvas = document.createElement("canvas");
  const maskCtx = maskCanvas.getContext("2d");

  if (!maskCtx) {
    throw new Error("无法创建遮罩画布上下文");
  }

  maskCanvas.width = canvas.width;
  maskCanvas.height = canvas.height;

  // 1. 先填充黑色背景（未涂抹区域）
  maskCtx.fillStyle = "black";
  maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height);

  // 2. 获取原始涂抹数据
  const imageData = canvas
    .getContext("2d")
    ?.getImageData(0, 0, canvas.width, canvas.height);
  if (!imageData) return maskCanvas;

  // 3. 创建白色遮罩数据（涂抹区域）
  const maskImageData = maskCtx.createImageData(canvas.width, canvas.height);
  const srcData = imageData.data;
  const maskData = maskImageData.data;

  for (let i = 0; i < srcData.length; i += 4) {
    // 如果原始像素有透明度（即被涂抹过）
    if (srcData[i + 3] > 0) {
      // 涂抹区域 = 白色
      maskData[i] = 255; // R = 255 (白色)
      maskData[i + 1] = 255; // G = 255 (白色)
      maskData[i + 2] = 255; // B = 255 (白色)
      maskData[i + 3] = 255; // A = 255 (完全不透明)
    } else {
      // 未涂抹区域 = 黑色
      maskData[i] = 0; // R = 0 (黑色)
      maskData[i + 1] = 0; // G = 0 (黑色)
      maskData[i + 2] = 0; // B = 0 (黑色)
      maskData[i + 3] = 255; // A = 255 (完全不透明)
    }
  }

  maskCtx.putImageData(maskImageData, 0, 0);
  return maskCanvas;
}

/**
 * 创建Alpha遮罩图片（参考iOS的inpaintingAlphaMaskImage方法）
 * 白色背景，涂抹区域透明
 */
export function createAlphaMaskImage(blackWhiteMask: HTMLCanvasElement): File {
  // 创建Alpha遮罩画布
  const alphaMaskCanvas = document.createElement("canvas");
  const alphaMaskCtx = alphaMaskCanvas.getContext("2d");

  if (!alphaMaskCtx) {
    throw new Error("无法创建Alpha遮罩画布上下文");
  }

  alphaMaskCanvas.width = blackWhiteMask.width;
  alphaMaskCanvas.height = blackWhiteMask.height;

  // 1. 整张填白（不透明背景）- 对应iOS的白色填充
  alphaMaskCtx.fillStyle = "white";
  alphaMaskCtx.fillRect(0, 0, alphaMaskCanvas.width, alphaMaskCanvas.height);

  // 2. 直接使用黑白遮罩，通过destination-out清除白色区域
  alphaMaskCtx.globalCompositeOperation = "destination-out";

  // 创建临时画布，只绘制白色区域（涂抹区域）
  const tempCanvas = document.createElement("canvas");
  const tempCtx = tempCanvas.getContext("2d");
  if (tempCtx) {
    tempCanvas.width = blackWhiteMask.width;
    tempCanvas.height = blackWhiteMask.height;

    // 绘制黑白遮罩
    tempCtx.drawImage(blackWhiteMask, 0, 0);

    // 获取图像数据并只保留白色区域
    const imageData = tempCtx.getImageData(
      0,
      0,
      tempCanvas.width,
      tempCanvas.height,
    );
    const data = imageData.data;

    for (let i = 0; i < data.length; i += 4) {
      // 只保留白色像素（涂抹区域），其他设为透明
      if (data[i] > 128) {
        // 白色像素
        data[i] = 255; // R
        data[i + 1] = 255; // G
        data[i + 2] = 255; // B
        data[i + 3] = 255; // A
      } else {
        data[i] = 0; // R
        data[i + 1] = 0; // G
        data[i + 2] = 0; // B
        data[i + 3] = 0; // A (透明)
      }
    }

    tempCtx.putImageData(imageData, 0, 0);
    alphaMaskCtx.drawImage(tempCanvas, 0, 0);
  }

  // 转换为File对象
  try {
    const base64 = alphaMaskCanvas.toDataURL("image/png");
    if (!base64 || !base64.startsWith("data:") || !base64.includes(",")) {
      throw new Error("Invalid data URL generated from alpha mask canvas");
    }
    return base64ToFile(base64, "mask.png");
  } catch (error) {
    console.error("Error generating alpha mask data URL:", error);
    // 返回一个空的PNG文件作为fallback
    return new File([EMPTY_PNG_BYTES], "mask.png", { type: "image/png" });
  }
}

/**
 * 创建遮罩图片（组合方法）
 */
export function createMaskImage(canvas: HTMLCanvasElement): File {
  const blackWhiteMask = createBlackWhiteMask(canvas);
  return createAlphaMaskImage(blackWhiteMask);
}

/**
 * 设置绘制工具属性（参考iOS实现）
 */
export function setupDrawingTool(
  ctx: CanvasRenderingContext2D,
  tool: "brush" | "eraser",
  brushSize: number,
): void {
  if (tool === "brush") {
    // 恢复模式：绘制白色（在最终遮罩中表示涂抹区域）
    ctx.globalCompositeOperation = "source-over";
    ctx.strokeStyle = "rgba(255, 255, 255, 0.8)"; // 白色半透明
  } else {
    // 擦除模式：使用destination-out清除像素
    ctx.globalCompositeOperation = "destination-out";
    ctx.strokeStyle = "rgba(255, 255, 255, 1.0)"; // 完全不透明用于擦除
  }
  ctx.lineWidth = brushSize;
  ctx.lineCap = "round";
  ctx.lineJoin = "round";
}

/**
 * 绘制圆点（用于单点绘制）
 */
export function drawDot(
  ctx: CanvasRenderingContext2D,
  tool: "brush" | "eraser",
  x: number,
  y: number,
  brushSize: number,
): void {
  if (tool === "brush") {
    // 恢复模式：绘制白色圆点
    ctx.globalCompositeOperation = "source-over";
    ctx.fillStyle = "rgba(255, 255, 255, 0.8)";
  } else {
    // 擦除模式：清除圆点区域
    ctx.globalCompositeOperation = "destination-out";
    ctx.fillStyle = "rgba(255, 255, 255, 1.0)";
  }
  ctx.beginPath();
  ctx.arc(x, y, brushSize / 2, 0, Math.PI * 2);
  ctx.fill();
}

/**
 * 安全的toDataURL调用，带错误处理
 */
export function safeToDataURL(
  canvas: HTMLCanvasElement,
  type: string = "image/png",
): string | null {
  try {
    const dataURL = canvas.toDataURL(type);
    if (!dataURL || !dataURL.startsWith("data:") || !dataURL.includes(",")) {
      throw new Error("Invalid data URL generated");
    }
    return dataURL;
  } catch (error) {
    console.error("Error generating data URL:", error);
    return null;
  }
}

/**
 * 检查画布是否有涂抹内容
 */
export function checkCanvasHasContent(canvas: HTMLCanvasElement): boolean {
  try {
    const ctx = canvas.getContext("2d");
    if (!ctx) return false;

    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // 检查是否有非透明像素（即有涂抹内容）
    for (let i = 3; i < data.length; i += 4) {
      if (data[i] > 0) {
        // Alpha通道大于0表示有内容
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error("Error checking canvas content:", error);
    return false;
  }
}
