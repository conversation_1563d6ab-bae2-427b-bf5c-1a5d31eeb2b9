<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退货政策</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
        }
        h2 {
            color: #dc3545;
            margin-top: 30px;
        }
        h3 {
            color: #555;
            margin-top: 20px;
        }
        h4 {
            color: #666;
            margin-top: 15px;
        }
        .date-info {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
        }
        .highlight {
            background-color: #f8d7da;
            padding: 15px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        .section {
            margin-bottom: 25px;
        }
        .important {
            font-weight: bold;
            color: #dc3545;
        }
        .note {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
            font-size: 0.9em;
        }
        .success {
            background-color: #d4edda;
            padding: 10px;
            border-left: 4px solid #28a745;
            margin: 10px 0;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin: 5px 0;
        }
        .process-step {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>退货政策</h1>
        
        <div class="date-info">
            最后更新：2025年8月27日<br>
            生效日期：2025年8月27日
        </div>

        <div class="highlight">
            我们致力于为您提供满意的服务体验。如果您对我们的服务不满意，我们提供合理的退货和退款政策。
        </div>

        <div class="section">
            <h2>1. 退货政策概述</h2>
            
            <p>我们理解您可能因为各种原因需要申请退货或退款。本退货政策旨在保护您的合法权益，同时确保服务的公平性和可持续性。</p>
            
            <div class="note">
                <strong>重要提醒：</strong>由于我们提供的是数字服务和AI生成内容，退货政策与传统实物商品有所不同。请仔细阅读以下条款。
            </div>
        </div>

        <div class="section">
            <h2>2. 适用范围</h2>
            
            <h3>2.1 可退货的服务</h3>
            <ul>
                <li>付费订阅服务（在特定条件下）</li>
                <li>单次付费的高级功能</li>
                <li>购买的积分或代币（未使用部分）</li>
                <li>因技术故障导致的服务中断</li>
            </ul>
            
            <h3>2.2 不可退货的情况</h3>
            <ul>
                <li><span class="important">已经使用的AI生成服务</span></li>
                <li>已下载或保存的生成内容</li>
                <li>因用户违反服务条款导致的账户限制</li>
                <li>超过退货期限的申请</li>
                <li>恶意申请退货的行为</li>
            </ul>
        </div>

        <div class="section">
            <h2>3. 退货条件</h2>
            
            <h3>3.1 时间限制</h3>
            <div class="process-step">
                <strong>订阅服务：</strong>自购买之日起7天内可申请退货<br>
                <strong>单次付费：</strong>自购买之日起48小时内可申请退货<br>
                <strong>技术故障：</strong>故障解决后30天内可申请补偿
            </div>
            
            <h3>3.2 退货理由</h3>
            <p>我们接受以下合理的退货理由：</p>
            <ul>
                <li>服务功能与描述不符</li>
                <li>技术故障导致无法正常使用</li>
                <li>重复购买</li>
                <li>未成年人未经监护人同意的购买</li>
                <li>其他合理的消费者权益保护理由</li>
            </ul>
            
            <h3>3.3 退货要求</h3>
            <ul>
                <li>提供有效的购买凭证</li>
                <li>详细说明退货原因</li>
                <li>配合我们的调查和验证</li>
                <li>承诺不再使用相关服务</li>
            </ul>
        </div>

        <div class="section">
            <h2>4. 退货流程</h2>
            
            <h3>4.1 申请退货</h3>
            <div class="process-step">
                <strong>步骤1：</strong>通过应用内客服或邮件联系我们<br>
                <strong>步骤2：</strong>提供订单号和退货理由<br>
                <strong>步骤3：</strong>等待客服审核（1-3个工作日）<br>
                <strong>步骤4：</strong>审核通过后处理退款
            </div>
            
            <h3>4.2 审核标准</h3>
            <p>我们将根据以下标准审核退货申请：</p>
            <ul>
                <li>是否符合退货条件</li>
                <li>是否在规定时间内申请</li>
                <li>退货理由是否合理</li>
                <li>是否存在滥用退货政策的行为</li>
            </ul>
            
            <h3>4.3 退款处理</h3>
            <div class="success">
                <strong>退款方式：</strong>原路退回至您的付款账户<br>
                <strong>处理时间：</strong>审核通过后3-7个工作日<br>
                <strong>退款金额：</strong>扣除已使用部分后的剩余金额
            </div>
        </div>

        <div class="section">
            <h2>5. 特殊情况处理</h2>
            
            <h3>5.1 技术故障补偿</h3>
            <p>如果因为我们的技术故障导致您无法正常使用服务，我们将提供以下补偿：</p>
            <ul>
                <li>延长服务期限</li>
                <li>提供等值的服务积分</li>
                <li>在特殊情况下提供现金退款</li>
            </ul>
            
            <h3>5.2 部分退款</h3>
            <p>对于已部分使用的服务，我们将按照以下原则处理：</p>
            <ul>
                <li>按使用比例计算退款金额</li>
                <li>保留合理的服务费用</li>
                <li>确保退款的公平性</li>
            </ul>
            
            <h3>5.3 争议解决</h3>
            <p>如果您对退货处理结果不满意，可以：</p>
            <ul>
                <li>申请上级客服介入</li>
                <li>通过消费者权益保护渠道投诉</li>
                <li>寻求第三方调解</li>
            </ul>
        </div>

        <div class="section">
            <h2>6. 防止滥用</h2>
            
            <div class="note">
                <strong>重要声明：</strong>我们保留拒绝恶意退货申请的权利。以下行为将被视为滥用退货政策：
            </div>
            
            <ul>
                <li>频繁申请退货</li>
                <li>使用服务后恶意申请退货</li>
                <li>提供虚假信息</li>
                <li>利用退货政策获取不当利益</li>
            </ul>
            
            <p><span class="important">对于滥用退货政策的用户，我们可能会限制其退货权利或终止服务</span>。</p>
        </div>

        <div class="section">
            <h2>7. 政策更新</h2>
            
            <p>我们可能会根据业务发展和法律法规要求更新本退货政策。重大变更将通过以下方式通知您：</p>
            <ul>
                <li>应用内通知</li>
                <li>邮件通知</li>
                <li>官网公告</li>
            </ul>
        </div>

        <div class="section">
            <h2>联系我们</h2>
            <p>如果您需要申请退货或对退货政策有任何疑问，请通过以下方式联系我们：</p>
            <ul>
                <li><strong>邮箱：</strong><EMAIL></li>
                <li><strong>客服热线：</strong>400-123-4567（工作日9:00-18:00）</li>
                <li><strong>在线客服：</strong>通过应用内客服功能</li>
                <li><strong>工单系统：</strong>通过官网提交退货申请</li>
            </ul>
            
            <div class="success">
                我们承诺在收到您的退货申请后，会尽快处理并及时回复。感谢您对我们服务的理解和支持！
            </div>
        </div>
    </div>
</body>
</html>
